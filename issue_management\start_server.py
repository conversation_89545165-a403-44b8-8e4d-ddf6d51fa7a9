#!/usr/bin/env python3
"""
DN Issue Management System 启动脚本
支持HTTP和HTTPS模式启动
"""

import os
import sys
import argparse
import subprocess
import ssl
from pathlib import Path

def generate_ssl_cert(cert_file='cert.pem', key_file='key.pem'):
    """生成自签名SSL证书"""
    if os.path.exists(cert_file) and os.path.exists(key_file):
        print(f"SSL证书文件已存在: {cert_file}, {key_file}")
        return True
    
    print("生成自签名SSL证书...")
    try:
        cmd = [
            'openssl', 'req', '-x509', '-newkey', 'rsa:4096', '-nodes',
            '-out', cert_file, '-keyout', key_file, '-days', '365',
            '-subj', '/C=CN/ST=Beijing/L=Beijing/O=DN/OU=IT/CN=localhost'
        ]
        result = subprocess.run(cmd, capture_output=True, text=True)
        if result.returncode == 0:
            print(f"SSL证书生成成功: {cert_file}, {key_file}")
            return True
        else:
            print(f"SSL证书生成失败: {result.stderr}")
            return False
    except FileNotFoundError:
        print("错误: 未找到openssl命令。请安装OpenSSL。")
        return False
    except Exception as e:
        print(f"生成SSL证书时发生错误: {e}")
        return False

def start_server(mode='https', env='development', port=None):
    """启动服务器"""
    
    # 设置环境变量
    os.environ['FLASK_CONFIG'] = env
    
    if mode == 'https':
        # 生成SSL证书（如果不存在）
        if not generate_ssl_cert():
            print("SSL证书生成失败，回退到HTTP模式")
            mode = 'http'
    
    if mode == 'https':
        os.environ['FORCE_HTTPS'] = 'true'
        default_port = 5443
    else:
        os.environ['FORCE_HTTPS'] = 'false'
        default_port = 5000
    
    # 设置端口
    if port is None:
        port = default_port
    
    print(f"启动模式: {mode.upper()}")
    print(f"环境: {env}")
    print(f"端口: {port}")
    print("-" * 50)
    
    # 启动Flask应用
    try:
        from app import app
        
        if mode == 'https':
            # HTTPS模式
            cert_file = 'cert.pem'
            key_file = 'key.pem'
            
            if os.path.exists(cert_file) and os.path.exists(key_file):
                context = ssl.SSLContext(ssl.PROTOCOL_TLS_SERVER)
                context.load_cert_chain(cert_file, key_file)
                
                app.run(
                    host='0.0.0.0',
                    port=port,
                    debug=(env == 'development'),
                    ssl_context=context
                )
            else:
                print("SSL证书文件不存在，无法启动HTTPS服务器")
                sys.exit(1)
        else:
            # HTTP模式
            app.run(
                host='0.0.0.0',
                port=port,
                debug=(env == 'development')
            )
    
    except KeyboardInterrupt:
        print("\n服务器已停止")
    except Exception as e:
        print(f"启动服务器时发生错误: {e}")
        sys.exit(1)

def main():
    parser = argparse.ArgumentParser(description='DN Issue Management System 启动脚本')
    parser.add_argument('--mode', choices=['http', 'https'], default='https',
                       help='启动模式 (默认: https)')
    parser.add_argument('--env', choices=['development', 'production', 'testing'], 
                       default='development', help='运行环境 (默认: development)')
    parser.add_argument('--port', type=int, help='端口号 (默认: HTTP=5000, HTTPS=5443)')
    parser.add_argument('--generate-cert', action='store_true',
                       help='只生成SSL证书，不启动服务器')
    
    args = parser.parse_args()
    
    if args.generate_cert:
        generate_ssl_cert()
        return
    
    start_server(args.mode, args.env, args.port)

if __name__ == '__main__':
    main() 