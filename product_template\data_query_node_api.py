#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据查询器节点API
流程编排引擎 - 数据查询器节点的后端处理逻辑
"""

from flask import Flask, request, jsonify
from flask_cors import CORS
from datetime import datetime
import sqlite3
import json
import re
import uuid
from typing import Dict, List, Any, Optional
import logging
from dataclasses import dataclass
from enum import Enum

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)
CORS(app)

class UserGroup(Enum):
    """用户组枚举"""
    YE = "YE"
    MODULE = "MODULE"

class NodeStatus(Enum):
    """节点状态枚举"""
    DRAFT = "draft"
    ACTIVE = "active"
    INACTIVE = "inactive"
    ERROR = "error"

class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self, db_path: str = "data_query_nodes.db"):
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """初始化数据库"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # 创建节点配置表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS node_configs (
                    id TEXT PRIMARY KEY,
                    name TEXT NOT NULL,
                    config_json TEXT NOT NULL,
                    status TEXT NOT NULL,
                    created_by TEXT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # 创建执行历史表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS execution_history (
                    id TEXT PRIMARY KEY,
                    node_id TEXT NOT NULL,
                    input_params TEXT NOT NULL,
                    output_data TEXT,
                    status TEXT NOT NULL,
                    error_message TEXT,
                    execution_time REAL,
                    executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # 创建模拟数据表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS defect_data (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    product_id TEXT NOT NULL,
                    lot_id TEXT NOT NULL,
                    layer_id TEXT NOT NULL,
                    wafer_id TEXT NOT NULL,
                    inspection_tool TEXT NOT NULL,
                    defect_class TEXT,
                    defect_code TEXT,
                    defect_count INTEGER DEFAULT 0,
                    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            conn.commit()
        
        # 插入模拟数据
        self.insert_sample_data()
    
    def insert_sample_data(self):
        """插入模拟测试数据"""
        sample_data = [
            ("PROD001", "LOT001", "LAYER001", "WAFER001", "KLA-Tencor", "Particle", "P001", 5),
            ("PROD001", "LOT001", "LAYER001", "WAFER002", "KLA-Tencor", "Scratch", "S001", 3),
            ("PROD001", "LOT001", "LAYER002", "WAFER001", "Applied Materials", "Stain", "ST001", 2),
            ("PROD002", "LOT002", "LAYER001", "WAFER003", "ASML", "Pattern", "PT001", 8),
            ("PROD002", "LOT002", "LAYER001", "WAFER004", "Tokyo Electron", "Other", "O001", 1),
        ]
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM defect_data")
            if cursor.fetchone()[0] == 0:  # 只在表为空时插入数据
                cursor.executemany("""
                    INSERT INTO defect_data 
                    (product_id, lot_id, layer_id, wafer_id, inspection_tool, defect_class, defect_code, defect_count)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                """, sample_data)
                conn.commit()

class NodeValidator:
    """节点配置验证器"""
    
    @staticmethod
    def validate_input_params(params: list) -> list:
        """验证输入参数（数组）"""
        errors = []
        if not params or not isinstance(params, list):
            errors.append("至少需要一个输入参数")
            return errors
        names = set()
        for p in params:
            name = p.get('name', '').strip()
            if not name:
                errors.append("参数名不能为空")
            if name in names:
                errors.append(f"参数名重复: {name}")
            names.add(name)
            if 'required' not in p or not isinstance(p['required'], bool):
                errors.append(f"参数 {name} 的必填属性无效")
        return errors

    @staticmethod
    def validate_permissions(permissions) -> list:
        errors = []
        if not permissions or not isinstance(permissions, list):
            errors.append("请至少选择一个用户组权限")
        return errors

    @staticmethod
    def validate_sql_query(sql_query: str, input_params: list) -> list:
        errors = []
        if not sql_query.strip():
            errors.append("SQL查询语句不能为空")
            return errors
        dangerous_keywords = ['DROP', 'DELETE', 'UPDATE', 'INSERT', 'ALTER', 'CREATE', 'TRUNCATE']
        sql_upper = sql_query.upper()
        for keyword in dangerous_keywords:
            if keyword in sql_upper:
                errors.append(f"SQL查询包含危险关键词: {keyword}")
        return errors

class QueryExecutor:
    """查询执行器"""
    
    def __init__(self, db_manager: DatabaseManager):
        self.db_manager = db_manager
    
    def execute_query(self, node_config: Dict, input_params: Dict) -> Dict:
        """执行查询"""
        start_time = datetime.now()
        
        try:
            # 直接使用输入参数，不需要转换
            sql_params = input_params.get('inputParams', {})
            
            # 执行SQL查询
            with sqlite3.connect(self.db_manager.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()
                
                # 替换SQL中的参数
                sql_query = node_config['sqlConfig']['query']
                sql_query = self._replace_sql_params(sql_query, sql_params)
                
                logger.info(f"执行SQL查询: {sql_query}")
                cursor.execute(sql_query)
                
                results = [dict(row) for row in cursor.fetchall()]
                
                execution_time = (datetime.now() - start_time).total_seconds()
                
                return {
                    'success': True,
                    'data': results,
                    'count': len(results),
                    'execution_time': execution_time,
                    'executed_at': datetime.now().isoformat()
                }
                
        except Exception as e:
            execution_time = (datetime.now() - start_time).total_seconds()
            logger.error(f"查询执行失败: {str(e)}")
            
            return {
                'success': False,
                'error': str(e),
                'execution_time': execution_time,
                'executed_at': datetime.now().isoformat()
            }
    
    def _replace_sql_params(self, sql_query: str, params: Dict) -> str:
        """替换SQL中的参数"""
        # 为每个参数创建占位符值
        test_values = {
            'productId': 'PROD001',
            'lotId': 'LOT001', 
            'layerId': 'LAYER001',
            'waferList': ['WAFER001', 'WAFER002'],
            'time': '2024-01-01 00:00:00',
            'inspectionTool': 'KLA-Tencor',
            'defectClass': 'Particle',
            'defectCode': 'P001'
        }
        
        # 处理参数替换
        for param_name in params.keys():
            placeholder = f':{param_name}'
            if placeholder in sql_query:
                # 使用测试值或默认值
                if param_name in test_values:
                    value = test_values[param_name]
                    if isinstance(value, list):
                        placeholders = ','.join([f"'{v}'" for v in value])
                        sql_query = sql_query.replace(placeholder, f'({placeholders})')
                    else:
                        sql_query = sql_query.replace(placeholder, f"'{value}'")
                else:
                    # 使用通用测试值
                    sql_query = sql_query.replace(placeholder, "'test_value'")
        
        return sql_query

# 初始化组件
db_manager = DatabaseManager()
validator = NodeValidator()
executor = QueryExecutor(db_manager)

@app.route('/')
def index():
    """主页"""
    with open('data_query_node_editor.html', 'r', encoding='utf-8') as f:
        return f.read()

@app.route('/api/node/validate', methods=['POST'])
def validate_node():
    """验证节点配置"""
    try:
        data = request.get_json()
        errors = []
        input_params = data.get('inputParams', [])
        errors.extend(validator.validate_input_params(input_params))
        permissions = data.get('permissions', [])
        errors.extend(validator.validate_permissions(permissions))
        sql_config = data.get('sqlConfig', {})
        sql_query = sql_config.get('query', '')
        errors.extend(validator.validate_sql_query(sql_query, input_params))
        return jsonify({
            'valid': len(errors) == 0,
            'errors': errors
        })
    except Exception as e:
        logger.error(f"验证失败: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/node/save', methods=['POST'])
def save_node():
    """保存节点配置"""
    try:
        data = request.get_json()
        user_group = request.headers.get('X-User-Group', 'MODULE')
        if user_group != 'YE':
            return jsonify({'error': '权限不足，只有YE用户组可以编辑节点'}), 403
        errors = []
        input_params = data.get('inputParams', [])
        errors.extend(validator.validate_input_params(input_params))
        permissions = data.get('permissions', [])
        errors.extend(validator.validate_permissions(permissions))
        sql_config = data.get('sqlConfig', {})
        sql_query = sql_config.get('query', '')
        errors.extend(validator.validate_sql_query(sql_query, input_params))
        if errors:
            return jsonify({'valid': False, 'errors': errors}), 400
        node_id = str(uuid.uuid4())
        node_name = f"数据查询器节点_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        with sqlite3.connect(db_manager.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("""
                INSERT INTO node_configs (id, name, config_json, status, created_by)
                VALUES (?, ?, ?, ?, ?)
            """, (node_id, node_name, json.dumps(data), 'active', 'current_user'))
            conn.commit()
        return jsonify({
            'success': True,
            'node_id': node_id,
            'message': '节点配置保存成功'
        })
    except Exception as e:
        logger.error(f"保存失败: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/node/test', methods=['POST'])
def test_query():
    """测试查询（不保存配置）"""
    try:
        data = request.get_json()
        errors = []
        input_params = data.get('inputParams', [])
        errors.extend(validator.validate_input_params(input_params))
        permissions = data.get('permissions', [])
        errors.extend(validator.validate_permissions(permissions))
        sql_config = data.get('sqlConfig', {})
        sql_query = sql_config.get('query', '')
        errors.extend(validator.validate_sql_query(sql_query, input_params))
        if errors:
            return jsonify({
                'success': False,
                'errors': errors
            })
        execution_result = executor.execute_query(data, data)
        return jsonify(execution_result)
    except Exception as e:
        logger.error(f"测试失败: {str(e)}")
        return jsonify({'error': str(e)}), 500

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=3001) 