// 节点编辑器主要功能
class NodeEditor {
    constructor() {
        this.nodes = new Map();
        this.connections = new Map();
        this.selectedNode = null;
        this.draggedNode = null;
        this.currentWorkflow = null;
        this.zoomLevel = 1;
        this.isConnecting = false;
        this.connectionStart = null;
        
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.setupDragAndDrop();
        this.loadWorkflow();
    }

    setupEventListeners() {
        // 节点库折叠
        document.addEventListener('click', (e) => {
            if (e.target.closest('[onclick="toggleLibrary()"]')) {
                this.toggleLibrary();
            }
        });

        // 画布工具栏
        document.addEventListener('click', (e) => {
            if (e.target.closest('[onclick="zoomIn()"]')) {
                this.zoomIn();
            } else if (e.target.closest('[onclick="zoomOut()"]')) {
                this.zoomOut();
            } else if (e.target.closest('[onclick="resetZoom()"]')) {
                this.resetZoom();
            } else if (e.target.closest('[onclick="clearCanvas()"]')) {
                this.clearCanvas();
            }
        });

        // 保存和返回
        document.addEventListener('click', (e) => {
            if (e.target.closest('[onclick="saveWorkflow()"]')) {
                this.saveWorkflow();
            } else if (e.target.closest('[onclick="goBack()"]')) {
                this.goBack();
            }
        });

        // 模态框
        document.addEventListener('click', (e) => {
            if (e.target.closest('[onclick="closeModal()"]')) {
                this.closeModal();
            } else if (e.target.closest('[onclick="saveNodeConfig()"]')) {
                this.saveNodeConfig();
            }
        });

        // 属性面板
        document.addEventListener('click', (e) => {
            if (e.target.closest('[onclick="closePropertyPanel()"]')) {
                this.closePropertyPanel();
            }
        });

        // 键盘快捷键
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Delete' && this.selectedNode) {
                this.deleteNode(this.selectedNode);
            } else if (e.key === 'Escape') {
                this.deselectAll();
            }
        });
    }

    setupDragAndDrop() {
        const canvas = document.getElementById('canvasGrid');
        const nodeItems = document.querySelectorAll('.node-item');

        // 设置节点库项目的拖拽
        nodeItems.forEach(item => {
            item.addEventListener('dragstart', (e) => {
                e.dataTransfer.setData('text/plain', item.dataset.nodeType);
                item.classList.add('dragging');
            });

            item.addEventListener('dragend', (e) => {
                item.classList.remove('dragging');
            });
        });

        // 画布拖拽处理
        canvas.addEventListener('dragover', (e) => {
            e.preventDefault();
            canvas.classList.add('drag-over');
        });

        canvas.addEventListener('dragleave', (e) => {
            canvas.classList.remove('drag-over');
        });

        canvas.addEventListener('drop', (e) => {
            e.preventDefault();
            canvas.classList.remove('drag-over');
            
            const nodeType = e.dataTransfer.getData('text/plain');
            const rect = canvas.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;
            
            this.createNode(nodeType, x, y);
        });
    }

    createNode(nodeType, x, y) {
        const nodeId = `node_${Date.now()}`;
        const nodeConfig = this.getNodeConfig(nodeType);
        
        const nodeElement = document.createElement('div');
        nodeElement.className = 'canvas-node new';
        nodeElement.dataset.nodeId = nodeId;
        nodeElement.dataset.nodeType = nodeType;
        nodeElement.style.left = `${x}px`;
        nodeElement.style.top = `${y}px`;
        
        nodeElement.innerHTML = `
            <div class="canvas-node-header">
                <div class="canvas-node-icon">${nodeConfig.icon}</div>
                <div class="canvas-node-title">${nodeConfig.title}</div>
            </div>
            <div class="canvas-node-body">
                <div class="canvas-node-desc">${nodeConfig.description}</div>
                <div class="canvas-node-status">未配置</div>
            </div>
            <div class="connection-point input" data-type="input"></div>
            <div class="connection-point output" data-type="output"></div>
        `;

        // 添加事件监听
        nodeElement.addEventListener('click', (e) => {
            e.stopPropagation();
            this.selectNode(nodeId);
        });

        nodeElement.addEventListener('dblclick', (e) => {
            e.stopPropagation();
            this.configureNode(nodeId);
        });

        // 添加到画布
        document.getElementById('canvasGrid').appendChild(nodeElement);
        
        // 保存节点数据
        this.nodes.set(nodeId, {
            id: nodeId,
            type: nodeType,
            x: x,
            y: y,
            config: nodeConfig.defaultConfig || {},
            element: nodeElement
        });

        // 自动选择新创建的节点
        this.selectNode(nodeId);
        
        // 移除new类
        setTimeout(() => {
            nodeElement.classList.remove('new');
        }, 300);
    }

    getNodeConfig(nodeType) {
        const configs = {
            'case-info': {
                icon: 'CI',
                title: 'Case Info',
                description: 'Case基础信息查询节点',
                defaultConfig: {
                    requiredParams: ['Time', 'Product ID', 'Lot ID', 'Layer ID', 'Inspection Tool', 'Wafer List'],
                    optionalParams: ['Defect Class', 'Defect Code'],
                    permissions: ['YE'],
                    autoExecute: true,
                    userFeedback: false
                }
            },
            'scan-info-query': {
                icon: 'SQ',
                title: 'Scan Info Query',
                description: 'Lot检测信息查询',
                defaultConfig: {
                    queryType: 'scan_info',
                    dataSource: 'inspection_db'
                }
            },
            'lot-history-query': {
                icon: 'LQ',
                title: 'Lot History Query',
                description: 'Lot履历信息查询',
                defaultConfig: {
                    queryType: 'lot_history',
                    dataSource: 'mes_db'
                }
            },
            'scan-info-table': {
                icon: 'ST',
                title: 'Scan Info Table',
                description: '垂直表展示',
                defaultConfig: {
                    displayType: 'table',
                    orientation: 'vertical'
                }
            },
            'lot-history-table': {
                icon: 'LT',
                title: 'Lot History Table',
                description: '履历表展示',
                defaultConfig: {
                    displayType: 'table',
                    orientation: 'vertical'
                }
            },
            'map-gallery': {
                icon: 'MG',
                title: 'Map Gallery',
                description: 'Map图和Image展示',
                defaultConfig: {
                    displayType: 'gallery',
                    imageTypes: ['map', 'image']
                }
            },
            'trend-chart': {
                icon: 'TC',
                title: 'Trend Chart',
                description: '趋势图表展示',
                defaultConfig: {
                    chartType: 'line',
                    timeRange: '7d'
                }
            },
            'defect-analysis': {
                icon: 'DA',
                title: 'Defect Analysis',
                description: '缺陷分析处理',
                defaultConfig: {
                    analysisType: 'statistical',
                    algorithms: ['clustering', 'classification']
                }
            },
            'pattern-match': {
                icon: 'PM',
                title: 'Pattern Match',
                description: '模式匹配分析',
                defaultConfig: {
                    matchType: 'similarity',
                    threshold: 0.8
                }
            },
            'ai-diagnosis': {
                icon: 'AI',
                title: 'AI Diagnosis',
                description: 'AI智能诊断',
                defaultConfig: {
                    model: 'defect_classifier',
                    confidence: 0.9
                }
            },
            'condition': {
                icon: 'IF',
                title: 'Condition',
                description: '条件判断',
                defaultConfig: {
                    conditionType: 'if_else',
                    operator: 'equals'
                }
            },
            'loop': {
                icon: 'LP',
                title: 'Loop',
                description: '循环处理',
                defaultConfig: {
                    loopType: 'for_each',
                    maxIterations: 100
                }
            },
            'parallel': {
                icon: 'PR',
                title: 'Parallel',
                description: '并行处理',
                defaultConfig: {
                    parallelType: 'concurrent',
                    maxThreads: 4
                }
            }
        };

        return configs[nodeType] || {
            icon: '??',
            title: 'Unknown',
            description: '未知节点类型',
            defaultConfig: {}
        };
    }

    selectNode(nodeId) {
        // 取消之前的选择
        if (this.selectedNode) {
            const prevNode = this.nodes.get(this.selectedNode);
            if (prevNode) {
                prevNode.element.classList.remove('selected');
            }
        }

        // 选择新节点
        this.selectedNode = nodeId;
        const node = this.nodes.get(nodeId);
        if (node) {
            node.element.classList.add('selected');
            this.showNodeProperties(node);
        }
    }

    showNodeProperties(node) {
        const panel = document.getElementById('panelContent');
        const nodeConfig = this.getNodeConfig(node.type);
        
        let html = `
            <div class="form-group">
                <label class="form-label">节点名称</label>
                <input type="text" class="form-control" value="${nodeConfig.title}" readonly>
            </div>
            <div class="form-group">
                <label class="form-label">节点类型</label>
                <input type="text" class="form-control" value="${node.type}" readonly>
            </div>
            <div class="form-group">
                <label class="form-label">描述</label>
                <textarea class="form-control form-textarea" readonly>${nodeConfig.description}</textarea>
            </div>
        `;

        // 根据节点类型添加特定配置
        if (node.type === 'case-info') {
            html += this.getCaseInfoConfigHTML(node.config);
        } else if (node.type.includes('query')) {
            html += this.getQueryConfigHTML(node.config);
        } else if (node.type.includes('table') || node.type.includes('gallery')) {
            html += this.getDisplayConfigHTML(node.config);
        }

        html += `
            <div class="form-group">
                <button class="btn btn-primary" onclick="nodeEditor.configureNode('${node.id}')">
                    <i class="fas fa-cog"></i> 配置节点
                </button>
                <button class="btn btn-secondary" onclick="nodeEditor.deleteNode('${node.id}')">
                    <i class="fas fa-trash"></i> 删除节点
                </button>
            </div>
        `;

        panel.innerHTML = html;
    }

    getCaseInfoConfigHTML(config) {
        return `
            <div class="form-group">
                <label class="form-label">必选参数</label>
                <div class="param-list">
                    ${config.requiredParams ? config.requiredParams.map(param => `
                        <div class="param-item">
                            <span class="param-name">${param}</span>
                            <span class="param-required">必须</span>
                        </div>
                    `).join('') : ''}
                </div>
            </div>
            <div class="form-group">
                <label class="form-label">可选参数</label>
                <div class="param-list">
                    ${config.optionalParams ? config.optionalParams.map(param => `
                        <div class="param-item">
                            <span class="param-name">${param}</span>
                            <span class="param-required">可选</span>
                        </div>
                    `).join('') : ''}
                </div>
            </div>
        `;
    }

    getQueryConfigHTML(config) {
        return `
            <div class="form-group">
                <label class="form-label">查询类型</label>
                <input type="text" class="form-control" value="${config.queryType || ''}" readonly>
            </div>
            <div class="form-group">
                <label class="form-label">数据源</label>
                <input type="text" class="form-control" value="${config.dataSource || ''}" readonly>
            </div>
        `;
    }

    getDisplayConfigHTML(config) {
        return `
            <div class="form-group">
                <label class="form-label">显示类型</label>
                <input type="text" class="form-control" value="${config.displayType || ''}" readonly>
            </div>
        `;
    }

    configureNode(nodeId) {
        const node = this.nodes.get(nodeId);
        if (!node) return;

        const modal = document.getElementById('nodeConfigModal');
        const modalTitle = document.getElementById('modalTitle');
        const modalBody = document.getElementById('modalBody');
        
        modalTitle.textContent = `配置 ${this.getNodeConfig(node.type).title}`;
        modalBody.innerHTML = this.getNodeConfigModal(node);
        
        modal.classList.add('show');
    }

    getNodeConfigModal(node) {
        if (node.type === 'case-info') {
            return this.getCaseInfoModalHTML(node);
        }
        
        return `
            <div class="form-group">
                <label class="form-label">节点配置</label>
                <textarea class="form-control form-textarea" placeholder="请输入节点配置...">${JSON.stringify(node.config, null, 2)}</textarea>
            </div>
        `;
    }

    getCaseInfoModalHTML(node) {
        return `
            <div class="form-group">
                <label class="form-label">输入参数</label>
                <div class="param-list" id="inputParams">
                    ${node.config.requiredParams ? node.config.requiredParams.map(param => `
                        <div class="param-item">
                            <input type="text" class="form-control param-name" value="${param}" placeholder="参数名">
                            <label class="switch param-required">
                                <input type="checkbox" checked>
                                <span class="slider"></span>
                            </label>
                            <div class="param-actions">
                                <button class="param-btn param-btn-settings" onclick="nodeEditor.configParam(this)">
                                    <i class="fas fa-cog"></i>
                                </button>
                                <button class="param-btn param-btn-remove" onclick="nodeEditor.removeParam(this)">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    `).join('') : ''}
                </div>
                <button class="add-param-btn" onclick="nodeEditor.addParam('inputParams')">
                    <i class="fas fa-plus"></i> 添加参数
                </button>
            </div>
            
            <div class="form-group">
                <label class="form-label">权限设置</label>
                <div class="permission-grid">
                    ${['YE', 'Module', 'QA', 'Admin'].map(role => `
                        <div class="permission-item">
                            <input type="checkbox" id="perm_${role}" ${node.config.permissions && node.config.permissions.includes(role) ? 'checked' : ''}>
                            <label for="perm_${role}">${role}</label>
                        </div>
                    `).join('')}
                </div>
            </div>
            
            <div class="form-group">
                <label class="form-label">节点选项</label>
                <div class="param-item">
                    <span class="param-name">自动执行</span>
                    <label class="switch">
                        <input type="checkbox" ${node.config.autoExecute ? 'checked' : ''}>
                        <span class="slider"></span>
                    </label>
                </div>
                <div class="param-item">
                    <span class="param-name">用户反馈</span>
                    <label class="switch">
                        <input type="checkbox" ${node.config.userFeedback ? 'checked' : ''}>
                        <span class="slider"></span>
                    </label>
                </div>
                <div class="param-item">
                    <span class="param-name">Issue动态交互</span>
                    <label class="switch">
                        <input type="checkbox" ${node.config.issueInteraction ? 'checked' : ''}>
                        <span class="slider"></span>
                    </label>
                </div>
            </div>
        `;
    }

    // 工具栏功能
    toggleLibrary() {
        const library = document.querySelector('.node-library');
        library.classList.toggle('collapsed');
    }

    zoomIn() {
        this.zoomLevel = Math.min(this.zoomLevel + 0.1, 2);
        this.updateZoom();
    }

    zoomOut() {
        this.zoomLevel = Math.max(this.zoomLevel - 0.1, 0.5);
        this.updateZoom();
    }

    resetZoom() {
        this.zoomLevel = 1;
        this.updateZoom();
    }

    updateZoom() {
        const canvas = document.getElementById('canvasGrid');
        canvas.style.transform = `scale(${this.zoomLevel})`;
        document.querySelector('.zoom-level').textContent = `${Math.round(this.zoomLevel * 100)}%`;
    }

    clearCanvas() {
        if (confirm('确定要清空画布吗？此操作不可撤销。')) {
            this.nodes.clear();
            this.connections.clear();
            this.selectedNode = null;
            document.getElementById('canvasGrid').innerHTML = '';
            this.closePropertyPanel();
        }
    }

    saveWorkflow() {
        const workflowData = {
            title: document.getElementById('workflowTitle').textContent,
            nodes: Array.from(this.nodes.values()).map(node => ({
                id: node.id,
                type: node.type,
                x: node.x,
                y: node.y,
                config: node.config
            })),
            connections: Array.from(this.connections.values())
        };

        console.log('保存工作流:', workflowData);
        alert('工作流已保存！');
    }

    goBack() {
        if (confirm('确定要返回吗？未保存的更改将丢失。')) {
            window.history.back();
        }
    }

    closeModal() {
        document.getElementById('nodeConfigModal').classList.remove('show');
    }

    closePropertyPanel() {
        document.getElementById('panelContent').innerHTML = `
            <div class="empty-state">
                <i class="fas fa-mouse-pointer"></i>
                <p>请选择一个节点来编辑属性</p>
            </div>
        `;
        this.selectedNode = null;
        document.querySelectorAll('.canvas-node.selected').forEach(node => {
            node.classList.remove('selected');
        });
    }

    deleteNode(nodeId) {
        if (confirm('确定要删除此节点吗？')) {
            const node = this.nodes.get(nodeId);
            if (node) {
                node.element.remove();
                this.nodes.delete(nodeId);
                
                if (this.selectedNode === nodeId) {
                    this.closePropertyPanel();
                }
            }
        }
    }

    deselectAll() {
        this.selectedNode = null;
        document.querySelectorAll('.canvas-node.selected').forEach(node => {
            node.classList.remove('selected');
        });
        this.closePropertyPanel();
    }

    loadWorkflow() {
        // 加载默认工作流或从服务器加载
        console.log('加载工作流...');
    }

    // 参数配置相关方法
    addParam(containerId) {
        const container = document.getElementById(containerId);
        const paramItem = document.createElement('div');
        paramItem.className = 'param-item';
        paramItem.innerHTML = `
            <input type="text" class="form-control param-name" placeholder="参数名">
            <label class="switch param-required">
                <input type="checkbox">
                <span class="slider"></span>
            </label>
            <div class="param-actions">
                <button class="param-btn param-btn-settings" onclick="nodeEditor.configParam(this)">
                    <i class="fas fa-cog"></i>
                </button>
                <button class="param-btn param-btn-remove" onclick="nodeEditor.removeParam(this)">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        `;
        container.appendChild(paramItem);
    }

    removeParam(button) {
        button.closest('.param-item').remove();
    }

    configParam(button) {
        // 配置参数的SQL查询等
        alert('参数配置功能开发中...');
    }

    saveNodeConfig() {
        // 保存节点配置
        console.log('保存节点配置...');
        this.closeModal();
    }
}

// 初始化节点编辑器
const nodeEditor = new NodeEditor();

// 全局函数（用于HTML onclick事件）
function toggleLibrary() { nodeEditor.toggleLibrary(); }
function zoomIn() { nodeEditor.zoomIn(); }
function zoomOut() { nodeEditor.zoomOut(); }
function resetZoom() { nodeEditor.resetZoom(); }
function clearCanvas() { nodeEditor.clearCanvas(); }
function saveWorkflow() { nodeEditor.saveWorkflow(); }
function goBack() { nodeEditor.goBack(); }
function closeModal() { nodeEditor.closeModal(); }
function saveNodeConfig() { nodeEditor.saveNodeConfig(); }
function closePropertyPanel() { nodeEditor.closePropertyPanel(); } 