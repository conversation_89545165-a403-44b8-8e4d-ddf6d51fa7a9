#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试工作流配置的脚本
"""

import sys
import os
sys.path.append('issue_management')

import sqlite3
import json

def test_workflow_config():
    """测试工作流配置"""
    print("🔍 测试工作流配置...")
    
    # 连接数据库
    db_path = 'issue_management/dn_issues.db'
    if not os.path.exists(db_path):
        print("❌ 数据库文件不存在，请先运行 python issue_management/app.py")
        return
    
    conn = sqlite3.connect(db_path)
    conn.row_factory = sqlite3.Row
    
    # 查询 dn-rd 工作流
    workflow = conn.execute('''
        SELECT id, name, description, config FROM workflows WHERE id = 'dn-rd'
    ''').fetchone()

    if not workflow:
        print("❌ 未找到 dn-rd 工作流")
        return
    
    print(f"✅ 找到工作流: {workflow['name']}")
    print(f"📝 描述: {workflow['description']}")
    
    # 解析配置
    try:
        config = json.loads(workflow['config'])
        nodes = config.get('nodes', [])
        connections = config.get('connections', [])
        
        print(f"\n📊 节点统计:")
        print(f"   总节点数: {len(nodes)}")
        print(f"   连接数: {len(connections)}")
        
        print(f"\n🔗 工作流程:")
        
        # 按层级显示节点
        layers = {
            1: [],  # HOLD LOT LIST 查询
            2: [],  # HOLD LOT LIST 结果展示
            3: [],  # CASE INFO
            4: [],  # 并行查询节点组
            5: [],  # 并行展示节点组
            6: [],  # CASE CONFIRM
            7: [],  # 并行处理节点组
            8: []   # SEND EMAIL
        }
        
        # 根据Y坐标分层
        for node in nodes:
            y = node['position']['y']
            if y <= 100:
                layers[1].append(node)
            elif y <= 200:
                layers[2].append(node)
            elif y <= 300:
                layers[3].append(node)
            elif y <= 400:
                layers[4].append(node)
            elif y <= 500:
                layers[5].append(node)
            elif y <= 600:
                layers[6].append(node)
            elif y <= 700:
                layers[7].append(node)
            else:
                layers[8].append(node)
        
        # 显示每层节点
        layer_names = {
            1: "第1层 - 初始查询",
            2: "第2层 - 结果展示", 
            3: "第3层 - 案例信息",
            4: "第4层 - 并行查询",
            5: "第5层 - 并行展示",
            6: "第6层 - 案例确认",
            7: "第7层 - 并行处理",
            8: "第8层 - 邮件发送"
        }
        
        for layer_num, layer_nodes in layers.items():
            if layer_nodes:
                print(f"\n   {layer_names[layer_num]}:")
                for node in layer_nodes:
                    print(f"     • {node['icon']} - {node['title']}")
        
        print(f"\n🔄 连接关系:")
        for conn in connections:
            from_node = next((n for n in nodes if n['id'] == conn['from']), None)
            to_node = next((n for n in nodes if n['id'] == conn['to']), None)
            if from_node and to_node:
                print(f"   {from_node['icon']} → {to_node['icon']} ({from_node['title']} → {to_node['title']})")
        
        print(f"\n✅ 工作流配置测试完成!")
        
    except json.JSONDecodeError as e:
        print(f"❌ 配置解析失败: {e}")
    
    conn.close()

if __name__ == '__main__':
    test_workflow_config()
