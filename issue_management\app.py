from flask import Flask, jsonify, request, send_from_directory, session, redirect, render_template
from flask_cors import CORS
import sqlite3
import json
from datetime import datetime, timedelta
import os
import random
import ssl
from werkzeug.security import generate_password_hash, check_password_hash
from functools import wraps
from config import config

app = Flask(__name__)

# 加载配置
config_name = os.environ.get('FLASK_CONFIG', 'default')
app.config.from_object(config[config_name])

CORS(app, supports_credentials=True)  # 允许跨域请求
app.secret_key = 'your-secret-key-here'  # 在生产环境中应该使用更安全的密钥

# 数据库配置
DATABASE = 'dn_issues.db'

def get_db_connection():
    """获取数据库连接"""
    conn = sqlite3.connect(DATABASE)
    conn.row_factory = sqlite3.Row
    return conn

def init_database():
    """初始化数据库，创建所需的表"""
    conn = get_db_connection()
    
    # 创建用户表
    conn.execute('''
        CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT UNIQUE NOT NULL,
            password_hash TEXT NOT NULL,
            email TEXT UNIQUE NOT NULL,
            role TEXT NOT NULL DEFAULT 'user',
            department TEXT,
            module TEXT NOT NULL DEFAULT 'ye',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            last_login TIMESTAMP
        )
    ''')
    
    # 创建issues表
    conn.execute('''
        CREATE TABLE IF NOT EXISTS issues (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            title TEXT NOT NULL,
            description TEXT,
            status TEXT DEFAULT 'open',
            priority TEXT DEFAULT 'medium',
            assignee TEXT,
            assignee_id INTEGER,
            creator_id INTEGER,
            workflow_id INTEGER,
            labels TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (assignee_id) REFERENCES users (id),
            FOREIGN KEY (creator_id) REFERENCES users (id)
        )
    ''')
    
    # 创建workflow_steps表
    conn.execute('''
        CREATE TABLE IF NOT EXISTS workflow_steps (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            issue_id INTEGER,
            step_name TEXT NOT NULL,
            step_type TEXT NOT NULL,
            status TEXT DEFAULT 'pending',
            data TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (issue_id) REFERENCES issues (id)
        )
    ''')
    
    # 创建缺陷检测案例表
    conn.execute('''
        CREATE TABLE IF NOT EXISTS defect_cases (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            case_id TEXT UNIQUE NOT NULL,
            product_id TEXT NOT NULL,
            lot_id TEXT NOT NULL,
            layer_id TEXT NOT NULL,
            tool TEXT NOT NULL,
            wafer_list TEXT,
            defect_class TEXT,
            defect_code TEXT,
            priority TEXT DEFAULT 'medium',
            status TEXT DEFAULT 'open',
            assignee TEXT,
            assignee_id INTEGER,
            creator_id INTEGER,
            wafer_count INTEGER,
            defect_count INTEGER,
            inspection_date TIMESTAMP,
            fab_location TEXT,
            process_step TEXT,
            comments TEXT,
            created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (assignee_id) REFERENCES users (id),
            FOREIGN KEY (creator_id) REFERENCES users (id)
        )
    ''')
    
    # 检查是否已有用户数据，如果没有则创建默认用户
    cursor = conn.execute('SELECT COUNT(*) FROM users')
    user_count = cursor.fetchone()[0]
    
    if user_count == 0:
        # 创建默认用户
        default_users = [
            ('admin', 'admin123', '<EMAIL>', 'admin', 'IT', 'admin'),
            ('zhang_san', 'zhang123', '<EMAIL>', 'user', 'QA', 'ye'),
            ('li_si', 'li123', '<EMAIL>', 'user', 'RD', 'ye'),
            ('wang_wu', 'wang123', '<EMAIL>', 'user', 'Production', 'ye'),
            ('zhao_liu', 'zhao123', '<EMAIL>', 'manager', 'QA', 'admin'),
            ('ye_user', 'ye123', '<EMAIL>', 'user', 'YE', 'ye'),
            ('admin_user', 'admin123', '<EMAIL>', 'user', 'Admin', 'admin')
        ]
        
        for username, password, email, role, department, module in default_users:
            password_hash = generate_password_hash(password)
            conn.execute('''
                INSERT INTO users (username, password_hash, email, role, department, module)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (username, password_hash, email, role, department, module))
    
    # 创建workflows表
    conn.execute('''
        CREATE TABLE IF NOT EXISTS workflows (
            id TEXT PRIMARY KEY,
            name TEXT NOT NULL,
            description TEXT,
            config TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # 检查是否已有数据
    existing_cases = conn.execute('SELECT COUNT(*) FROM defect_cases').fetchone()[0]
    
    if existing_cases == 0:
        # 插入模拟的半导体缺陷检测数据
        sample_data = generate_sample_defect_data()
        for case in sample_data:
            conn.execute('''
                INSERT INTO defect_cases (
                    case_id, product_id, lot_id, layer_id, tool, defect_class, defect_code,
                    priority, status, assignee, created_time, wafer_count, defect_count,
                    inspection_date, fab_location, process_step, comments
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                case['case_id'], case['product_id'], case['lot_id'], case['layer_id'],
                case['tool'], case['defect_class'], case['defect_code'], case['priority'],
                case['status'], case['assignee'], case['created_time'], case['wafer_count'],
                case['defect_count'], case['inspection_date'], case['fab_location'],
                case['process_step'], case['comments']
            ))
    
    # 插入默认工作流配置
    workflows_data = [
        {
            'id': 'dn-rd',
            'name': 'DN 目标工作流-RD',
            'description': 'Research & Development workflow for DN analysis',
            'config': json.dumps({
                'nodes': [
                    # 第一层：HOLD LOT LIST 查询
                    {
                        'id': 'hold_lot_query',
                        'title': 'Hold Lot List 查询',
                        'icon': 'HL',
                        'type': 'query',
                        'status': 'completed',
                        'permissions': ['ye', 'admin'],
                        'description': 'Hold状态Lot列表查询',
                        'position': {'x': 250, 'y': 50}
                    },
                    # 第二层：HOLD LOT LIST 结果展示
                    {
                        'id': 'hold_lot_display',
                        'title': 'Hold Lot List 结果展示',
                        'icon': 'HD',
                        'type': 'display',
                        'status': 'pending',
                        'permissions': ['ye', 'admin'],
                        'description': 'Hold Lot列表结果展示',
                        'position': {'x': 250, 'y': 150}
                    },
                    # 第三层：CASE INFO
                    {
                        'id': 'case_info',
                        'title': 'Case Info',
                        'icon': 'CI',
                        'type': 'query',
                        'status': 'pending',
                        'permissions': ['ye', 'admin'],
                        'description': 'Case基础信息查询',
                        'position': {'x': 250, 'y': 250}
                    },
                    # 第四层：并行查询节点组
                    {
                        'id': 'scan_info_query',
                        'title': 'Scan Info 查询',
                        'icon': 'SI',
                        'type': 'query',
                        'status': 'pending',
                        'permissions': ['ye', 'admin'],
                        'description': 'Lot检测信息查询',
                        'position': {'x': 50, 'y': 350}
                    },
                    {
                        'id': 'lot_history_query',
                        'title': 'Lot History 查询',
                        'icon': 'LH',
                        'type': 'query',
                        'status': 'pending',
                        'permissions': ['ye', 'admin'],
                        'description': 'Lot履历信息查询',
                        'position': {'x': 180, 'y': 350}
                    },
                    {
                        'id': 'map_gallery_query',
                        'title': 'Map Gallery 查询',
                        'icon': 'MG',
                        'type': 'query',
                        'status': 'pending',
                        'permissions': ['ye', 'admin'],
                        'description': 'Wafer Map图像画廊查询',
                        'position': {'x': 320, 'y': 350}
                    },
                    {
                        'id': 'trend_chart_query',
                        'title': 'Trend Chart 查询',
                        'icon': 'TC',
                        'type': 'query',
                        'status': 'pending',
                        'permissions': ['ye', 'admin'],
                        'description': '缺陷趋势图查询',
                        'position': {'x': 450, 'y': 350}
                    },
                    # 第五层：并行展示节点组
                    {
                        'id': 'scan_info_display',
                        'title': 'Scan Info 结果展示',
                        'icon': 'SD',
                        'type': 'display',
                        'status': 'pending',
                        'permissions': ['ye', 'admin'],
                        'description': 'Scan Info结果展示',
                        'position': {'x': 50, 'y': 450}
                    },
                    {
                        'id': 'lot_history_display',
                        'title': 'Lot History 结果展示',
                        'icon': 'LD',
                        'type': 'display',
                        'status': 'pending',
                        'permissions': ['ye', 'admin'],
                        'description': 'Lot History结果展示',
                        'position': {'x': 180, 'y': 450}
                    },
                    {
                        'id': 'map_gallery_display',
                        'title': 'Map Gallery 结果展示',
                        'icon': 'MD',
                        'type': 'display',
                        'status': 'pending',
                        'permissions': ['ye', 'admin'],
                        'description': 'Map Gallery结果展示',
                        'position': {'x': 320, 'y': 450}
                    },
                    {
                        'id': 'trend_chart_display',
                        'title': 'Trend Chart 结果展示',
                        'icon': 'TD',
                        'type': 'display',
                        'status': 'pending',
                        'permissions': ['ye', 'admin'],
                        'description': 'Trend Chart结果展示',
                        'position': {'x': 450, 'y': 450}
                    },
                    # 第六层：CASE CONFIRM
                    {
                        'id': 'case_confirm',
                        'title': 'Case Confirm',
                        'icon': 'CC',
                        'type': 'processing',
                        'status': 'pending',
                        'permissions': ['ye', 'admin'],
                        'description': 'Case确认处理',
                        'position': {'x': 250, 'y': 550}
                    },
                    # 第七层：并行处理节点组
                    {
                        'id': 'ye_comment',
                        'title': 'YE Comment',
                        'icon': 'YC',
                        'type': 'processing',
                        'status': 'pending',
                        'permissions': ['ye', 'admin'],
                        'description': 'YE工程师评论',
                        'position': {'x': 180, 'y': 650}
                    },
                    {
                        'id': 'ppt_preview',
                        'title': 'PPT 预览',
                        'icon': 'PP',
                        'type': 'display',
                        'status': 'pending',
                        'permissions': ['ye', 'admin'],
                        'description': 'PPT报告预览',
                        'position': {'x': 320, 'y': 650}
                    },
                    # 第八层：SEND EMAIL
                    {
                        'id': 'send_email',
                        'title': 'Send Email',
                        'icon': 'SE',
                        'type': 'processing',
                        'status': 'pending',
                        'permissions': ['ye', 'admin'],
                        'description': '发送邮件通知',
                        'position': {'x': 250, 'y': 750}
                    }
                ],
                'connections': [
                    # 主流程：HOLD LOT LIST 查询 → 结果展示 → CASE INFO
                    {'from': 'hold_lot_query', 'to': 'hold_lot_display'},
                    {'from': 'hold_lot_display', 'to': 'case_info'},

                    # CASE INFO → 四个并行查询分支
                    {'from': 'case_info', 'to': 'scan_info_query'},
                    {'from': 'case_info', 'to': 'lot_history_query'},
                    {'from': 'case_info', 'to': 'map_gallery_query'},
                    {'from': 'case_info', 'to': 'trend_chart_query'},

                    # 每个查询 → 对应的结果展示
                    {'from': 'scan_info_query', 'to': 'scan_info_display'},
                    {'from': 'lot_history_query', 'to': 'lot_history_display'},
                    {'from': 'map_gallery_query', 'to': 'map_gallery_display'},
                    {'from': 'trend_chart_query', 'to': 'trend_chart_display'},

                    # 所有结果展示 → CASE CONFIRM
                    {'from': 'scan_info_display', 'to': 'case_confirm'},
                    {'from': 'lot_history_display', 'to': 'case_confirm'},
                    {'from': 'map_gallery_display', 'to': 'case_confirm'},
                    {'from': 'trend_chart_display', 'to': 'case_confirm'},

                    # CASE CONFIRM → 两个并行分支
                    {'from': 'case_confirm', 'to': 'ye_comment'},
                    {'from': 'case_confirm', 'to': 'ppt_preview'},

                    # 两个并行分支 → SEND EMAIL
                    {'from': 'ye_comment', 'to': 'send_email'},
                    {'from': 'ppt_preview', 'to': 'send_email'}
                ]
            })
        },
        {
            'id': 'dn-qa',
            'name': 'DN 目标工作流-QA',
            'description': 'Quality Assurance workflow for DN analysis',
            'config': json.dumps({
                'sections': [
                    {
                        'title': 'QA质量检查',
                        'steps': [
                            {
                                'id': 'qa-check',
                                'icon': 'QC',
                                'type': 'quality',
                                'title': 'QA Check',
                                'description': 'QA质量检查节点',
                                'panel_type': 'quality',
                                'permissions': ['ye', 'admin']
                            }
                        ]
                    }
                ]
            })
        },
        {
            'id': 'dn-production',
            'name': 'DN 目标工作流-Production',
            'description': 'Production workflow for DN analysis with universal node architecture',
            'config': json.dumps({
                'nodes': [
                    # 第一层：HOLD LOT LIST 查询
                    {
                        'id': 'hold_lot_query',
                        'title': 'Hold Lot List 查询',
                        'icon': 'HL',
                        'type': 'query',
                        'status': 'completed',
                        'permissions': ['ye', 'admin'],
                        'description': 'Hold状态Lot列表查询',
                        'position': {'x': 400, 'y': 50}
                    },
                    # 第二层：HOLD LOT LIST 结果展示
                    {
                        'id': 'hold_lot_display',
                        'title': 'Hold Lot List 结果展示',
                        'icon': 'HD',
                        'type': 'display',
                        'status': 'completed',
                        'permissions': ['ye', 'admin'],
                        'description': 'Hold Lot列表结果展示',
                        'position': {'x': 400, 'y': 170}
                    },
                    # 第三层：CASE INFO
                    {
                        'id': 'case_info',
                        'title': 'Case Info',
                        'icon': 'CI',
                        'type': 'query',
                        'status': 'completed',
                        'permissions': ['ye', 'admin'],
                        'description': 'Case基础信息查询',
                        'position': {'x': 400, 'y': 290}
                    },
                    # 第四层：并行查询节点组
                    {
                        'id': 'scan_info_query',
                        'title': 'Scan Info 查询',
                        'icon': 'SI',
                        'type': 'query',
                        'status': 'pending',
                        'permissions': ['ye', 'admin'],
                        'description': 'Lot检测信息查询',
                        'position': {'x': 50, 'y': 420}
                    },
                    {
                        'id': 'scan_info_display',
                        'title': 'Scan Info 结果展示',
                        'icon': 'SD',
                        'type': 'display',
                        'status': 'pending',
                        'permissions': ['ye', 'admin'],
                        'description': 'Scan Info结果展示',
                        'position': {'x': 50, 'y': 540}
                    },
                    {
                        'id': 'lot_history_query',
                        'title': 'Lot History 查询',
                        'icon': 'LH',
                        'type': 'query',
                        'status': 'pending',
                        'permissions': ['ye', 'admin'],
                        'description': 'Lot履历信息查询',
                        'position': {'x': 250, 'y': 420}
                    },
                    {
                        'id': 'lot_history_display',
                        'title': 'Lot History 结果展示',
                        'icon': 'LD',
                        'type': 'display',
                        'status': 'pending',
                        'permissions': ['ye', 'admin'],
                        'description': 'Lot History结果展示',
                        'position': {'x': 250, 'y': 540}
                    },
                    {
                        'id': 'map_gallery_query',
                        'title': 'Map Gallery 查询',
                        'icon': 'MG',
                        'type': 'query',
                        'status': 'pending',
                        'permissions': ['ye', 'admin'],
                        'description': 'Wafer Map图像画廊查询',
                        'position': {'x': 550, 'y': 420}
                    },
                    {
                        'id': 'map_gallery_display',
                        'title': 'Map Gallery 结果展示',
                        'icon': 'MD',
                        'type': 'display',
                        'status': 'pending',
                        'permissions': ['ye', 'admin'],
                        'description': 'Map Gallery结果展示',
                        'position': {'x': 550, 'y': 540}
                    },
                    {
                        'id': 'trend_chart_query',
                        'title': 'Trend Chart 查询',
                        'icon': 'TC',
                        'type': 'query',
                        'status': 'pending',
                        'permissions': ['ye', 'admin'],
                        'description': '缺陷趋势图查询',
                        'position': {'x': 750, 'y': 420}
                    },
                    {
                        'id': 'trend_chart_display',
                        'title': 'Trend Chart 结果展示',
                        'icon': 'TD',
                        'type': 'display',
                        'status': 'pending',
                        'permissions': ['ye', 'admin'],
                        'description': 'Trend Chart结果展示',
                        'position': {'x': 750, 'y': 540}
                    },
                    # 第五层：CASE CONFIRM
                    {
                        'id': 'case_confirm',
                        'title': 'Case Confirm',
                        'icon': 'CC',
                        'type': 'processing',
                        'status': 'pending',
                        'permissions': ['ye', 'admin'],
                        'description': 'Case确认处理',
                        'position': {'x': 400, 'y': 670}
                    },
                    # 第六层：并行处理节点组
                    {
                        'id': 'ye_comment',
                        'title': 'YE Comment',
                        'icon': 'YC',
                        'type': 'processing',
                        'status': 'pending',
                        'permissions': ['ye', 'admin'],
                        'description': 'YE工程师评论',
                        'position': {'x': 250, 'y': 800}
                    },
                    {
                        'id': 'ppt_preview',
                        'title': 'PPT 预览',
                        'icon': 'PP',
                        'type': 'display',
                        'status': 'pending',
                        'permissions': ['ye', 'admin'],
                        'description': 'PPT报告预览',
                        'position': {'x': 550, 'y': 800}
                    },
                    # 第七层：SEND EMAIL
                    {
                        'id': 'send_email',
                        'title': 'Send Email',
                        'icon': 'SE',
                        'type': 'processing',
                        'status': 'pending',
                        'permissions': ['ye', 'admin'],
                        'description': '发送邮件通知',
                        'position': {'x': 400, 'y': 930}
                    }
                ],
                'connections': [
                    # 主流程：HOLD LOT LIST 查询 → 结果展示 → CASE INFO
                    {'source': 'hold_lot_query', 'target': 'hold_lot_display'},
                    {'source': 'hold_lot_display', 'target': 'case_info'},

                    # CASE INFO → 四个并行查询分支
                    {'source': 'case_info', 'target': 'scan_info_query'},
                    {'source': 'case_info', 'target': 'lot_history_query'},
                    {'source': 'case_info', 'target': 'map_gallery_query'},
                    {'source': 'case_info', 'target': 'trend_chart_query'},

                    # 每个查询 → 对应的结果展示
                    {'source': 'scan_info_query', 'target': 'scan_info_display'},
                    {'source': 'lot_history_query', 'target': 'lot_history_display'},
                    {'source': 'map_gallery_query', 'target': 'map_gallery_display'},
                    {'source': 'trend_chart_query', 'target': 'trend_chart_display'},

                    # 所有结果展示 → CASE CONFIRM
                    {'source': 'scan_info_display', 'target': 'case_confirm'},
                    {'source': 'lot_history_display', 'target': 'case_confirm'},
                    {'source': 'map_gallery_display', 'target': 'case_confirm'},
                    {'source': 'trend_chart_display', 'target': 'case_confirm'},

                    # CASE CONFIRM → 两个并行分支
                    {'source': 'case_confirm', 'target': 'ye_comment'},
                    {'source': 'case_confirm', 'target': 'ppt_preview'},

                    # 两个并行分支 → SEND EMAIL
                    {'source': 'ye_comment', 'target': 'send_email'},
                    {'source': 'ppt_preview', 'target': 'send_email'}
                ]
            })
        }
    ]
    
    # 强制更新工作流配置（确保每次启动都使用最新配置）
    for workflow in workflows_data:
        conn.execute('''
            INSERT OR REPLACE INTO workflows (id, name, description, config, created_at, updated_at)
            VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
        ''', (workflow['id'], workflow['name'], workflow['description'], workflow['config']))

    conn.commit()
    conn.close()

def generate_sample_defect_data():
    """生成样本缺陷检测数据"""
    products = ['P001', 'P002', 'P003', 'P004', 'P005']
    lots = ['LOT001', 'LOT002', 'LOT003', 'LOT004', 'LOT005']
    layers = ['M1', 'M2', 'M3', 'VIA1', 'VIA2']
    tools = ['AKLAC8', 'KLA-2900', 'AMAT-SEMVision', 'Hitachi-RS6000']
    defect_classes = ['Particle', 'Scratch', 'Stain', 'Pattern']
    defect_codes = ['DC001', 'DC002', 'DC003', 'DC004', 'DC005']
    priorities = ['high', 'medium', 'low']
    statuses = ['open', 'in-progress', 'closed']
    assignees = ['王五', '李四', '张三', '赵六']
    fab_locations = ['Fab1', 'Fab2', 'Fab3']
    process_steps = ['Etch', 'Deposition', 'Lithography', 'CMP', 'Implant']
    
    sample_data = []
    
    for i in range(50):  # 生成50条样本数据
        case_id = f"CASE-{str(i+1).zfill(4)}"
        product_id = random.choice(products)
        lot_id = random.choice(lots)
        layer_id = random.choice(layers)
        tool = random.choice(tools)
        defect_class = random.choice(defect_classes)
        defect_code = random.choice(defect_codes)
        priority = random.choice(priorities)
        status = random.choice(statuses)
        assignee = random.choice(assignees)
        
        # 生成随机时间
        base_time = datetime.now() - timedelta(days=random.randint(0, 30))
        created_time = base_time.strftime('%Y-%m-%d %H:%M:%S')
        inspection_date = (base_time - timedelta(hours=random.randint(1, 24))).strftime('%Y-%m-%d %H:%M:%S')
        
        wafer_count = random.randint(1, 25)
        defect_count = random.randint(1, 500)
        fab_location = random.choice(fab_locations)
        process_step = random.choice(process_steps)
        comments = f"Defect detected in {process_step} process"
        
        sample_data.append({
            'case_id': case_id,
            'product_id': product_id,
            'lot_id': lot_id,
            'layer_id': layer_id,
            'tool': tool,
            'defect_class': defect_class,
            'defect_code': defect_code,
            'priority': priority,
            'status': status,
            'assignee': assignee,
            'created_time': created_time,
            'wafer_count': wafer_count,
            'defect_count': defect_count,
            'inspection_date': inspection_date,
            'fab_location': fab_location,
            'process_step': process_step,
            'comments': comments
        })
    
    return sample_data

def generate_sample_data():
    """生成样本数据的包装函数"""
    # 这个函数可以用来生成额外的样本数据
    # 目前数据已经在 init_database() 中生成
    pass

# 登录验证装饰器
def login_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            return jsonify({'error': 'Authentication required'}), 401
        return f(*args, **kwargs)
    return decorated_function

# 获取当前用户信息
def get_current_user():
    try:
        if 'user_id' in session:
            conn = get_db_connection()
            user = conn.execute(
                'SELECT id, username, email, role, department, module FROM users WHERE id = ?',
                (session['user_id'],)
            ).fetchone()
            conn.close()
            if user:
                return dict(user)
        return None
    except Exception as e:
        print(f"Error in get_current_user: {e}")
        return None

# 用户登录
@app.route('/api/login', methods=['POST'])
def login():
    data = request.get_json()
    username = data.get('username')
    password = data.get('password')
    
    if not username or not password:
        return jsonify({'error': 'Username and password are required'}), 400
    
    conn = get_db_connection()
    user = conn.execute(
        'SELECT id, username, password_hash, email, role, department, module FROM users WHERE username = ?',
        (username,)
    ).fetchone()
    
    if user and check_password_hash(user['password_hash'], password):
        # 更新最后登录时间
        conn.execute(
            'UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE id = ?',
            (user['id'],)
        )
        conn.commit()
        
        # 设置会话
        session['user_id'] = user['id']
        session['username'] = user['username']
        session['role'] = user['role']
        session['department'] = user['department']
        session['module'] = user['module']
        
        conn.close()
        return jsonify({
            'message': 'Login successful',
            'user': {
                'id': user['id'],
                'username': user['username'],
                'email': user['email'],
                'role': user['role'],
                'department': user['department'],
                'module': user['module']
            }
        })
    
    conn.close()
    return jsonify({'error': 'Invalid credentials'}), 401

# 用户登出
@app.route('/api/logout', methods=['POST'])
def logout():
    session.clear()
    return jsonify({'message': 'Logout successful'})

# 获取当前用户信息
@app.route('/api/user', methods=['GET'])
@login_required
def get_user_info():
    try:
        user = get_current_user()
        if user:
            return jsonify({'user': user})
        return jsonify({'error': 'User not found'}), 404
    except Exception as e:
        print(f"Error in get_user_info: {e}")
        return jsonify({'error': 'Internal server error'}), 500

# 获取用户列表（管理员功能）
@app.route('/api/users', methods=['GET'])
@login_required
def get_users():
    user = get_current_user()
    if user['role'] != 'admin':
        return jsonify({'error': 'Access denied'}), 403
    
    conn = get_db_connection()
    users = conn.execute(
        'SELECT id, username, email, role, department, module, created_at, last_login FROM users ORDER BY created_at DESC'
    ).fetchall()
    conn.close()
    
    return jsonify({'users': [dict(user) for user in users]})

@app.route('/')
def index():
    # 检查用户是否已登录
    if 'user_id' not in session:
        return redirect('/login')
    return render_template('index.html')

@app.route('/login')
def login_page():
    # 如果用户已登录，重定向到主页
    if 'user_id' in session:
        return redirect('/')
    return render_template('login.html')

@app.route('/simple-login')
def simple_login_page():
    # 如果用户已登录，重定向到主页
    if 'user_id' in session:
        return redirect('/')
    return render_template('simple_login.html')

@app.route('/test')
def test():
    return render_template('test.html')

@app.route('/node-editor')
def node_editor():
    return render_template('node_editor.html')

@app.route('/static/<path:filename>')
def static_files(filename):
    return send_from_directory('static', filename)

@app.route('/api/defect-cases', methods=['GET'])
@login_required
def get_defect_cases():
    """获取缺陷检测案例列表"""
    current_user = get_current_user()
    
    # 获取查询参数
    product_id = request.args.get('product_id')
    lot_id = request.args.get('lot_id')
    layer_id = request.args.get('layer_id')
    tool = request.args.get('tool')
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')
    defect_class = request.args.get('defect_class')
    priority = request.args.get('priority')
    status = request.args.get('status')
    assignee = request.args.get('assignee')
    
    page = int(request.args.get('page', 1))
    page_size = int(request.args.get('page_size', 10))
    offset = (page - 1) * page_size
    
    try:
        conn = get_db_connection()
        
        # 构建查询条件
        where_conditions = []
        params = []
        
        # 根据用户权限过滤数据
        if current_user['role'] == 'user':
            # 普通用户只能看到自己的案例
            where_conditions.append('(creator_id = ? OR assignee_id = ?)')
            params.extend([current_user['id'], current_user['id']])
        elif current_user['role'] == 'manager':
            # 管理员可以看到同部门的案例
            where_conditions.append('''
                (creator_id = ? OR assignee_id = ? OR 
                 creator_id IN (SELECT id FROM users WHERE department = ?) OR
                 assignee_id IN (SELECT id FROM users WHERE department = ?))
            ''')
            params.extend([current_user['id'], current_user['id'], 
                          current_user['department'], current_user['department']])
        # admin角色可以看到所有案例，不添加额外条件
        
        # 添加其他过滤条件
        if product_id:
            where_conditions.append('product_id = ?')
            params.append(product_id)
        if lot_id:
            where_conditions.append('lot_id = ?')
            params.append(lot_id)
        if layer_id:
            where_conditions.append('layer_id = ?')
            params.append(layer_id)
        if tool:
            where_conditions.append('tool = ?')
            params.append(tool)
        if defect_class:
            where_conditions.append('defect_class = ?')
            params.append(defect_class)
        if priority:
            where_conditions.append('priority = ?')
            params.append(priority)
        if status:
            where_conditions.append('status = ?')
            params.append(status)
        if assignee:
            where_conditions.append('assignee = ?')
            params.append(assignee)
        if start_date:
            where_conditions.append('created_time >= ?')
            params.append(start_date)
        if end_date:
            where_conditions.append('created_time <= ?')
            params.append(end_date)
        
        where_clause = ' AND '.join(where_conditions) if where_conditions else '1=1'
        
        # 获取总数
        count_query = f'SELECT COUNT(*) FROM defect_cases WHERE {where_clause}'
        total_count = conn.execute(count_query, params).fetchone()[0]
        
        # 获取数据
        query = f'''
            SELECT dc.*, u1.username as creator_name, u2.username as assignee_name
            FROM defect_cases dc
            LEFT JOIN users u1 ON dc.creator_id = u1.id
            LEFT JOIN users u2 ON dc.assignee_id = u2.id
            WHERE {where_clause}
            ORDER BY dc.created_time DESC
            LIMIT ? OFFSET ?
        '''
        
        cases = conn.execute(query, params + [page_size, offset]).fetchall()
        conn.close()
        
        return jsonify({
            'cases': [dict(case) for case in cases],
            'total_count': total_count,
            'page': page,
            'page_size': page_size,
            'total_pages': (total_count + page_size - 1) // page_size
        })
        
    except Exception as e:
        import traceback
        print(f"Error in get_defect_cases: {str(e)}")
        print(f"Traceback: {traceback.format_exc()}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/workflows/<workflow_id>/nodes/<node_id>/status', methods=['PUT'])
@login_required
def update_node_status(workflow_id, node_id):
    """更新工作流节点状态"""
    try:
        data = request.get_json()
        new_status = data.get('status')

        if not new_status:
            return jsonify({'error': 'Status is required'}), 400

        # 验证状态值
        valid_statuses = ['pending', 'in_progress', 'completed', 'failed']
        if new_status not in valid_statuses:
            return jsonify({'error': 'Invalid status'}), 400

        conn = get_db_connection()

        # 获取工作流配置
        workflow = conn.execute(
            'SELECT config FROM workflows WHERE id = ?',
            (workflow_id,)
        ).fetchone()

        if not workflow:
            conn.close()
            return jsonify({'error': 'Workflow not found'}), 404

        # 解析配置
        config = json.loads(workflow['config'])

        # 查找并更新节点状态
        node_found = False
        for node in config.get('nodes', []):
            if node['id'] == node_id:
                node['status'] = new_status
                node_found = True
                break

        if not node_found:
            conn.close()
            return jsonify({'error': 'Node not found'}), 404

        # 保存更新后的配置
        conn.execute(
            'UPDATE workflows SET config = ?, updated_time = CURRENT_TIMESTAMP WHERE id = ?',
            (json.dumps(config), workflow_id)
        )
        conn.commit()
        conn.close()

        return jsonify({
            'success': True,
            'node_id': node_id,
            'status': new_status
        })

    except Exception as e:
        import traceback
        print(f"Error in update_node_status: {str(e)}")
        print(f"Traceback: {traceback.format_exc()}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/defect-cases/stats', methods=['GET'])
@login_required
def get_defect_cases_stats():
    """获取缺陷检测案例统计数据"""
    current_user = get_current_user()
    
    try:
        conn = get_db_connection()
        
        # 构建权限过滤条件
        where_conditions = []
        params = []
        
        if current_user['role'] == 'user':
            where_conditions.append('(creator_id = ? OR assignee_id = ?)')
            params.extend([current_user['id'], current_user['id']])
        elif current_user['role'] == 'manager':
            where_conditions.append('''
                (creator_id = ? OR assignee_id = ? OR 
                 creator_id IN (SELECT id FROM users WHERE department = ?) OR
                 assignee_id IN (SELECT id FROM users WHERE department = ?))
            ''')
            params.extend([current_user['id'], current_user['id'], 
                          current_user['department'], current_user['department']])
        
        where_clause = ' AND '.join(where_conditions) if where_conditions else '1=1'
        
        # 获取基础统计
        stats_query = f'''
            SELECT 
                COUNT(*) as total_cases,
                SUM(CASE WHEN status = 'open' THEN 1 ELSE 0 END) as pending_cases,
                SUM(CASE WHEN status = 'in-progress' THEN 1 ELSE 0 END) as in_progress_cases,
                SUM(CASE WHEN status = 'closed' THEN 1 ELSE 0 END) as completed_cases
            FROM defect_cases
            WHERE {where_clause}
        '''
        
        stats = conn.execute(stats_query, params).fetchone()
        
        # 获取工具分布
        tool_query = f'''
            SELECT tool, COUNT(*) as count
            FROM defect_cases
            WHERE {where_clause}
            GROUP BY tool
            ORDER BY count DESC
        '''
        
        tool_breakdown = {}
        for row in conn.execute(tool_query, params).fetchall():
            tool_breakdown[row['tool']] = row['count']
        
        # 获取缺陷类别分布
        defect_class_query = f'''
            SELECT defect_class, COUNT(*) as count
            FROM defect_cases
            WHERE {where_clause} AND defect_class IS NOT NULL
            GROUP BY defect_class
            ORDER BY count DESC
        '''
        
        defect_class_breakdown = {}
        for row in conn.execute(defect_class_query, params).fetchall():
            defect_class_breakdown[row['defect_class']] = row['count']
        
        # 获取趋势数据（最近30天）
        trend_query = f'''
            SELECT 
                DATE(created_time) as date,
                COUNT(*) as count
            FROM defect_cases
            WHERE {where_clause} AND created_time >= datetime('now', '-30 days')
            GROUP BY DATE(created_time)
            ORDER BY date DESC
        '''
        
        trend_data = []
        for row in conn.execute(trend_query, params).fetchall():
            trend_data.append({
                'date': row['date'],
                'count': row['count']
            })
        
        conn.close()
        
        return jsonify({
            'total_cases': stats['total_cases'],
            'pending_cases': stats['pending_cases'],
            'in_progress_cases': stats['in_progress_cases'],
            'completed_cases': stats['completed_cases'],
            'tool_breakdown': tool_breakdown,
            'defect_class_breakdown': defect_class_breakdown,
            'trend_data': trend_data
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/issues', methods=['GET'])
@login_required
def get_issues():
    """获取Issues列表"""
    current_user = get_current_user()
    
    try:
        conn = get_db_connection()
        
        # 构建权限过滤条件
        where_conditions = []
        params = []
        
        if current_user['role'] == 'user':
            where_conditions.append('(creator_id = ? OR assignee_id = ?)')
            params.extend([current_user['id'], current_user['id']])
        elif current_user['role'] == 'manager':
            where_conditions.append('''
                (creator_id = ? OR assignee_id = ? OR 
                 creator_id IN (SELECT id FROM users WHERE department = ?) OR
                 assignee_id IN (SELECT id FROM users WHERE department = ?))
            ''')
            params.extend([current_user['id'], current_user['id'], 
                          current_user['department'], current_user['department']])
        
        where_clause = ' AND '.join(where_conditions) if where_conditions else '1=1'
        
        query = f'''
            SELECT i.*, u1.username as creator_name, u2.username as assignee_name
            FROM issues i
            LEFT JOIN users u1 ON i.creator_id = u1.id
            LEFT JOIN users u2 ON i.assignee_id = u2.id
            WHERE {where_clause}
            ORDER BY i.created_at DESC
        '''
        
        issues = conn.execute(query, params).fetchall()
        conn.close()
        
        return jsonify([dict(issue) for issue in issues])
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/issues', methods=['POST'])
@login_required
def create_issue():
    """创建新Issue"""
    current_user = get_current_user()
    
    try:
        data = request.get_json()
        
        conn = get_db_connection()
        cursor = conn.execute('''
            INSERT INTO issues (title, description, status, priority, assignee, creator_id, workflow_id, labels)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            data.get('title'),
            data.get('description'),
            data.get('status', 'open'),
            data.get('priority', 'medium'),
            data.get('assignee'),
            current_user['id'],
            data.get('workflow_id', 1),
            data.get('labels', '')
        ))
        
        issue_id = cursor.lastrowid
        conn.commit()
        conn.close()
        
        return jsonify({'id': issue_id, 'message': 'Issue created successfully'}), 201
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/workflow-orchestration')
def workflow_orchestration():
    """工作流编排页面"""
    return render_template('workflow_orchestration.html')

@app.route('/workflow-test')
def workflow_test():
    """工作流API测试页面"""
    return render_template('workflow_test.html')

@app.route('/api/workflows', methods=['GET'])
def get_workflows():
    """获取所有工作流"""
    conn = get_db_connection()
    workflows = conn.execute('''
        SELECT id, name, description, config, created_at, updated_at
        FROM workflows
        ORDER BY name
    ''').fetchall()
    conn.close()
    
    workflows_list = []
    for workflow in workflows:
        workflow_dict = dict(workflow)
        if workflow_dict['config']:
            workflow_dict['config'] = json.loads(workflow_dict['config'])
        workflows_list.append(workflow_dict)
    
    return jsonify({'workflows': workflows_list})

@app.route('/api/workflows/<workflow_id>', methods=['GET'])
@login_required
def get_workflow(workflow_id):
    """获取特定工作流（基于用户权限过滤）"""
    current_user = get_current_user()
    user_module = current_user.get('module', 'ye')
    
    conn = get_db_connection()
    workflow = conn.execute('''
        SELECT id, name, description, config, created_at, updated_at
        FROM workflows WHERE id = ?
    ''', (workflow_id,)).fetchone()
    conn.close()
    
    if not workflow:
        return jsonify({'error': 'Workflow not found'}), 404
    
    workflow_dict = dict(workflow)
    if workflow_dict['config']:
        config = json.loads(workflow_dict['config'])
        
        # 根据用户module过滤节点
        if 'sections' in config:
            filtered_sections = []
            for section in config['sections']:
                filtered_steps = []
                for step in section.get('steps', []):
                    # 检查步骤权限
                    step_permissions = step.get('permissions', ['ye'])  # 默认ye权限
                    if user_module in step_permissions or user_module == 'admin':
                        filtered_steps.append(step)
                
                if filtered_steps:  # 只有当有可见步骤时才添加section
                    section['steps'] = filtered_steps
                    filtered_sections.append(section)
            
            config['sections'] = filtered_sections
        
        workflow_dict['config'] = config
    
    return jsonify(workflow_dict)

@app.route('/api/issues/<int:issue_id>/workflow-steps', methods=['GET'])
def get_workflow_steps(issue_id):
    """获取issue的工作流步骤"""
    conn = get_db_connection()
    steps = conn.execute('''
        SELECT id, step_id, step_name, status, config, result, created_at, updated_at
        FROM workflow_steps
        WHERE issue_id = ?
        ORDER BY created_at
    ''', (issue_id,)).fetchall()
    conn.close()
    
    steps_list = []
    for step in steps:
        step_dict = dict(step)
        if step_dict['config']:
            step_dict['config'] = json.loads(step_dict['config'])
        if step_dict['result']:
            step_dict['result'] = json.loads(step_dict['result'])
        steps_list.append(step_dict)
    
    return jsonify(steps_list)

@app.route('/api/issues/<int:issue_id>/workflow-steps', methods=['POST'])
def create_workflow_step(issue_id):
    """为issue创建工作流步骤"""
    data = request.get_json()
    
    if not data or not data.get('step_id') or not data.get('step_name'):
        return jsonify({'error': 'step_id and step_name are required'}), 400
    
    conn = get_db_connection()
    cursor = conn.cursor()
    
    config_json = json.dumps(data.get('config', {}))
    result_json = json.dumps(data.get('result', {}))
    
    cursor.execute('''
        INSERT INTO workflow_steps (issue_id, step_id, step_name, status, config, result)
        VALUES (?, ?, ?, ?, ?, ?)
    ''', (
        issue_id,
        data['step_id'],
        data['step_name'],
        data.get('status', 'pending'),
        config_json,
        result_json
    ))
    
    step_id = cursor.lastrowid
    
    # 获取新创建的步骤
    step = conn.execute('''
        SELECT id, step_id, step_name, status, config, result, created_at, updated_at
        FROM workflow_steps WHERE id = ?
    ''', (step_id,)).fetchone()
    
    conn.commit()
    conn.close()
    
    step_dict = dict(step)
    if step_dict['config']:
        step_dict['config'] = json.loads(step_dict['config'])
    if step_dict['result']:
        step_dict['result'] = json.loads(step_dict['result'])
    
    return jsonify(step_dict), 201

# 健康检查
@app.route('/api/health', methods=['GET'])
def health_check():
    """健康检查端点"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.now().isoformat(),
        'version': '1.0.0'
    })

# 错误处理
@app.errorhandler(404)
def not_found(error):
    return jsonify({'error': 'Not found'}), 404

@app.errorhandler(500)
def internal_error(error):
    return jsonify({'error': 'Internal server error'}), 500

# 节点编辑器API
@app.route('/api/node-templates', methods=['GET'])
def get_node_templates():
    """获取节点模板配置"""
    templates = {
        'case-info': {
            'id': 'case-info',
            'name': 'Case Info',
            'category': 'query',
            'icon': 'CI',
            'description': 'Case基础信息查询节点',
            'defaultConfig': {
                'requiredParams': [
                    {'name': 'Time', 'required': True, 'querySql': ''},
                    {'name': 'Product ID', 'required': True, 'querySql': ''},
                    {'name': 'Lot ID', 'required': True, 'querySql': ''},
                    {'name': 'Layer ID', 'required': True, 'querySql': ''},
                    {'name': 'Inspection Tool', 'required': True, 'querySql': ''},
                    {'name': 'Wafer List', 'required': True, 'querySql': ''}
                ],
                'optionalParams': [
                    {'name': 'Defect Class', 'required': False, 'querySql': ''},
                    {'name': 'Defect Code', 'required': False, 'querySql': ''}
                ],
                'permissions': ['YE'],
                'autoExecute': True,
                'userFeedback': False,
                'issueInteraction': False,
                'sqlQuery': 'SELECT * FROM case_info WHERE ...'
            }
        },
        'scan-info-query': {
            'id': 'scan-info-query',
            'name': 'Scan Info Query',
            'category': 'query',
            'icon': 'SQ',
            'description': 'Lot检测信息查询节点',
            'defaultConfig': {
                'queryType': 'scan_info',
                'dataSource': 'inspection_db',
                'sqlQuery': 'SELECT * FROM scan_info WHERE lot_id = ?'
            }
        },
        'lot-history-query': {
            'id': 'lot-history-query',
            'name': 'Lot History Query',
            'category': 'query',
            'icon': 'LQ',
            'description': 'Lot履历信息查询节点',
            'defaultConfig': {
                'queryType': 'lot_history',
                'dataSource': 'mes_db',
                'sqlQuery': 'SELECT * FROM lot_history WHERE lot_id = ?'
            }
        },
        'scan-info-table': {
            'id': 'scan-info-table',
            'name': 'Scan Info Table',
            'category': 'display',
            'icon': 'ST',
            'description': '垂直表展示节点',
            'defaultConfig': {
                'displayType': 'table',
                'orientation': 'vertical',
                'columns': []
            }
        },
        'lot-history-table': {
            'id': 'lot-history-table',
            'name': 'Lot History Table',
            'category': 'display',
            'icon': 'LT',
            'description': 'Lot履历表展示节点',
            'defaultConfig': {
                'displayType': 'table',
                'orientation': 'vertical',
                'columns': []
            }
        },
        'map-gallery': {
            'id': 'map-gallery',
            'name': 'Map Gallery',
            'category': 'display',
            'icon': 'MG',
            'description': 'Map图和Image展示节点',
            'defaultConfig': {
                'displayType': 'gallery',
                'imageTypes': ['map', 'image'],
                'layout': 'grid'
            }
        },
        'trend-chart': {
            'id': 'trend-chart',
            'name': 'Trend Chart',
            'category': 'display',
            'icon': 'TC',
            'description': '趋势图表展示节点',
            'defaultConfig': {
                'chartType': 'line',
                'timeRange': '7d',
                'metrics': []
            }
        },
        'defect-analysis': {
            'id': 'defect-analysis',
            'name': 'Defect Analysis',
            'category': 'analysis',
            'icon': 'DA',
            'description': '缺陷分析处理节点',
            'defaultConfig': {
                'analysisType': 'statistical',
                'algorithms': ['clustering', 'classification'],
                'parameters': {}
            }
        },
        'pattern-match': {
            'id': 'pattern-match',
            'name': 'Pattern Match',
            'category': 'analysis',
            'icon': 'PM',
            'description': '模式匹配分析节点',
            'defaultConfig': {
                'matchType': 'similarity',
                'threshold': 0.8,
                'algorithm': 'cosine'
            }
        },
        'ai-diagnosis': {
            'id': 'ai-diagnosis',
            'name': 'AI Diagnosis',
            'category': 'analysis',
            'icon': 'AI',
            'description': 'AI智能诊断节点',
            'defaultConfig': {
                'model': 'defect_classifier',
                'confidence': 0.9,
                'modelPath': '/models/defect_classifier.pkl'
            }
        },
        'condition': {
            'id': 'condition',
            'name': 'Condition',
            'category': 'control',
            'icon': 'IF',
            'description': '条件判断节点',
            'defaultConfig': {
                'conditionType': 'if_else',
                'operator': 'equals',
                'conditions': []
            }
        },
        'loop': {
            'id': 'loop',
            'name': 'Loop',
            'category': 'control',
            'icon': 'LP',
            'description': '循环处理节点',
            'defaultConfig': {
                'loopType': 'for_each',
                'maxIterations': 100,
                'breakCondition': ''
            }
        },
        'parallel': {
            'id': 'parallel',
            'name': 'Parallel',
            'category': 'control',
            'icon': 'PR',
            'description': '并行处理节点',
            'defaultConfig': {
                'parallelType': 'concurrent',
                'maxThreads': 4,
                'timeout': 300
            }
        }
    }
    
    return jsonify(templates)

@app.route('/api/workflows/<workflow_id>/nodes', methods=['GET'])
def get_workflow_nodes(workflow_id):
    """获取工作流的节点配置"""
    conn = get_db_connection()
    workflow = conn.execute('''
        SELECT config FROM workflows WHERE id = ?
    ''', (workflow_id,)).fetchone()
    conn.close()
    
    if not workflow:
        return jsonify({'error': 'Workflow not found'}), 404
    
    try:
        config = json.loads(workflow['config'])
        return jsonify(config.get('nodes', []))
    except:
        return jsonify([])

@app.route('/api/workflows/<workflow_id>/nodes', methods=['POST'])
def save_workflow_nodes(workflow_id):
    """保存工作流的节点配置"""
    data = request.get_json()
    
    if not data:
        return jsonify({'error': 'No data provided'}), 400
    
    conn = get_db_connection()
    
    # 获取现有工作流配置
    workflow = conn.execute('''
        SELECT config FROM workflows WHERE id = ?
    ''', (workflow_id,)).fetchone()
    
    if not workflow:
        return jsonify({'error': 'Workflow not found'}), 404
    
    try:
        config = json.loads(workflow['config']) if workflow['config'] else {}
        config['nodes'] = data.get('nodes', [])
        config['connections'] = data.get('connections', [])
        
        # 更新工作流配置
        conn.execute('''
            UPDATE workflows 
            SET config = ?, updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
        ''', (json.dumps(config), workflow_id))
        
        conn.commit()
        conn.close()
        
        return jsonify({'success': True, 'message': 'Workflow nodes saved successfully'})
    
    except Exception as e:
        conn.close()
        return jsonify({'error': str(e)}), 500

@app.route('/api/node-config/validate', methods=['POST'])
def validate_node_config():
    """验证节点配置"""
    data = request.get_json()
    
    if not data:
        return jsonify({'error': 'No data provided'}), 400
    
    node_type = data.get('type')
    config = data.get('config', {})
    
    # 基本验证逻辑
    errors = []
    warnings = []
    
    if node_type == 'case-info':
        # 验证必选参数
        required_params = config.get('requiredParams', [])
        if not required_params:
            errors.append('至少需要一个必选参数')
        
        # 验证权限设置
        permissions = config.get('permissions', [])
        if not permissions:
            warnings.append('未设置权限，默认所有用户可访问')
        
        # 验证SQL查询
        sql_query = config.get('sqlQuery', '').strip()
        if not sql_query:
            warnings.append('未配置SQL查询语句')
    
    elif node_type.endswith('-query'):
        # 查询节点验证
        sql_query = config.get('sqlQuery', '').strip()
        if not sql_query:
            errors.append('查询节点必须配置SQL语句')
        
        data_source = config.get('dataSource', '').strip()
        if not data_source:
            warnings.append('未指定数据源')
    
    elif node_type.endswith('-table') or node_type.endswith('-gallery'):
        # 显示节点验证
        display_type = config.get('displayType', '').strip()
        if not display_type:
            errors.append('显示节点必须指定显示类型')
    
    return jsonify({
        'valid': len(errors) == 0,
        'errors': errors,
        'warnings': warnings
    })

# 安全头部中间件
@app.after_request
def after_request(response):
    if app.config.get('SECURE_HEADERS', False):
        response.headers['X-Content-Type-Options'] = 'nosniff'
        response.headers['X-Frame-Options'] = 'DENY'
        response.headers['X-XSS-Protection'] = '1; mode=block'
        
        if app.config.get('FORCE_HTTPS', False):
            response.headers['Strict-Transport-Security'] = 'max-age=31536000; includeSubDomains'
    
    return response

# HTTPS重定向中间件
@app.before_request
def force_https():
    if app.config.get('FORCE_HTTPS', False) and not request.is_secure:
        if request.url.startswith('http://'):
            url = request.url.replace('http://', 'https://', 1)
            url = url.replace(':5000', ':5443')  # 重定向到HTTPS端口
            return redirect(url, code=301)

if __name__ == '__main__':
    # 初始化数据库
    init_database()

    # 生成样本数据（仅在开发环境）
    if config_name == 'development':
        generate_sample_data()

    print("DN Issue Management System 启动中...")
    print(f"环境: {config_name}")
    print("访问地址:")

    # 检查SSL证书文件是否存在
    cert_file = app.config['SSL_CERT_FILE']
    key_file = app.config['SSL_KEY_FILE']

    if os.path.exists(cert_file) and os.path.exists(key_file):
        print(f"  HTTPS: https://localhost:{app.config['HTTPS_PORT']}")
        print(f"  HTTP:  http://localhost:{app.config['HTTP_PORT']} (如果FORCE_HTTPS=False)")
        print("默认登录账号: admin / admin123")

        # 创建SSL上下文
        context = ssl.SSLContext(ssl.PROTOCOL_TLS_SERVER)
        context.load_cert_chain(cert_file, key_file)

        # 启动HTTPS服务器
        try:
            app.run(
                host=app.config['HOST'],
                port=app.config['HTTPS_PORT'],
                debug=app.config['DEBUG'],
                ssl_context=context
            )
        except Exception as e:
            print(f"HTTPS启动失败: {e}")
            print("回退到HTTP模式")
            app.run(
                host=app.config['HOST'],
                port=app.config['HTTP_PORT'],
                debug=app.config['DEBUG']
            )
    else:
        print(f"  HTTP: http://localhost:{app.config['HTTP_PORT']}")
        print("SSL证书文件不存在，使用HTTP模式启动")
        print("默认登录账号: admin / admin123")
        app.run(
            host=app.config['HOST'],
            port=app.config['HTTP_PORT'],
            debug=app.config['DEBUG']
        )




