# DN Product 项目

DN (Defect Navigation) 产品项目，包含半导体缺陷分析工作流管理系统和数据查询节点API。

## 项目结构

```
dn_product/
├── issue_management/          # DN Issue管理系统
│   ├── app.py                # Flask主应用
│   ├── requirements.txt      # Python依赖
│   ├── README.md            # 详细文档
│   └── ...                  # 其他文件
├── product_template/         # 数据查询节点API
│   ├── data_query_node_api.py # 数据查询API
│   ├── data_query_node_editor.html # 节点编辑器
│   └── ...                  # 其他文件
├── images/                   # 项目截图
└── README.md                # 项目总览（本文件）
```

## 子项目介绍

### 1. DN Issue Management System
基于Flask的DN Issue管理系统，提供类似GitHub Issue的界面风格，用于管理半导体缺陷分析工作流。

**主要功能：**
- Issue创建、编辑、删除管理
- 工作流模板支持（RD、QA、Production）
- 三栏布局的现代化UI
- 实时数据交互

### 2. 数据查询节点API
流程编排引擎的数据查询器节点，提供数据查询和节点配置功能。

**主要功能：**
- 节点配置管理
- SQL查询执行
- 参数验证
- 权限控制

## 快速启动

### 环境要求
- Python 3.7+
- conda 或 pip包管理器

### 方法一：使用 Conda 环境（推荐）

1. **创建并激活conda环境**
```bash
# 创建环境
conda create -n dn_product python=3.11 -y

# 激活环境
conda activate dn_product
```

2. **启动 DN Issue Management System**
```bash
# 进入项目目录
cd issue_management

# 安装依赖
pip install -r requirements.txt

# 启动服务
python app.py
```

3. **访问应用**
```
浏览器打开: http://localhost:3000
```

4. **启动数据查询节点API**
```bash
# 在另一个终端中，先激活环境
conda activate dn_product

# 进入项目目录
cd product_template

# 启动API服务
python data_query_node_api.py
```

5. **访问节点编辑器**
```
浏览器打开: data_query_node_editor.html
```

### 方法二：使用 pip 直接安装

#### 启动 DN Issue Management System

1. **进入项目目录**
```bash
cd issue_management
```

2. **安装依赖**
```bash
pip install -r requirements.txt
```

3. **启动服务**
```bash
python app.py
```

4. **访问应用**
```
浏览器打开: http://localhost:3000
```

#### 启动数据查询节点API

1. **进入项目目录**
```bash
cd product_template
```

2. **启动API服务**
```bash
python data_query_node_api.py
```

3. **访问节点编辑器**
```
浏览器打开: data_query_node_editor.html
```

## 开发指南

### 同时运行两个服务

如果需要同时运行两个服务，可以在不同的终端窗口中分别启动：

**终端1 - Issue Management System:**
```bash
conda activate dn_product
cd issue_management
python app.py
```

**终端2 - 数据查询节点API:**
```bash
conda activate dn_product
cd product_template
python data_query_node_api.py
```

### 环境管理

```bash
# 查看所有conda环境
conda env list

# 激活环境
conda activate dn_product

# 退出环境
conda deactivate

# 删除环境（如果需要）
conda env remove -n dn_product
```

### 端口说明
- Issue Management System: 端口 3000
- 数据查询节点API: 端口 5000（默认Flask端口）

## 技术栈

- **后端**: Flask, SQLite
- **前端**: HTML5, CSS3, JavaScript
- **数据库**: SQLite
- **API**: RESTful API
- **CORS**: 跨域支持
- **环境管理**: Conda

## 项目特点

1. **模块化设计**: 两个独立的子项目，可以单独部署
2. **现代化UI**: 深色主题，响应式设计
3. **完整的API**: RESTful API设计
4. **数据持久化**: SQLite数据库存储
5. **权限控制**: 用户组权限管理
6. **实时交互**: 前后端分离架构
7. **环境隔离**: 使用conda管理Python环境

## 文档

- 详细的Issue管理系统文档: `issue_management/README.md`
- 数据查询节点API文档: 查看源码注释

## 贡献

1. Fork 项目
2. 创建特性分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

MIT License 