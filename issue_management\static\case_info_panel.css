/* Case信息侧边栏样式 */
.case-info-sidebar {
    position: fixed;
    top: 0;
    right: -400px; /* 初始隐藏在右侧 */
    width: 400px;
    height: 100vh;
    background: var(--bg-primary);
    border-left: 1px solid var(--border-color);
    z-index: 1000;
    transition: right 0.3s ease;
    display: flex;
    flex-direction: column;
    box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
}

.case-info-sidebar.visible {
    right: 0;
}

.case-info-sidebar.collapsed {
    width: 60px;
}

.case-info-sidebar.collapsed .sidebar-content {
    display: none;
}

.case-info-sidebar.collapsed .sidebar-title h3,
.case-info-sidebar.collapsed .case-number {
    display: none;
}

/* 侧边栏头部 */
.case-info-sidebar .sidebar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--border-color);
    flex-shrink: 0;
}

.case-info-sidebar .sidebar-title {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.case-info-sidebar .sidebar-title h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
}

.case-number {
    font-size: 14px;
    color: var(--text-secondary);
    font-weight: 500;
}

.case-info-sidebar .sidebar-toggle {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 8px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.case-info-sidebar .sidebar-toggle:hover {
    background: var(--border-color);
    color: var(--text-primary);
}

/* 侧边栏内容 */
.case-info-sidebar .sidebar-content {
    flex: 1;
    overflow-y: auto;
    padding: 0;
}

/* 信息区域样式 */
.case-info-sidebar .info-section {
    border-bottom: 1px solid var(--border-color);
}

.case-info-sidebar .info-section:last-child {
    border-bottom: none;
}

.case-info-sidebar .info-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--border-color);
}

.case-info-sidebar .info-header h4 {
    margin: 0;
    font-size: 14px;
    font-weight: 600;
    color: var(--text-primary);
}

.case-info-sidebar .config-btn {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 4px 6px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.case-info-sidebar .config-btn:hover {
    background: var(--border-color);
    color: var(--text-primary);
}

.case-info-sidebar .config-btn i {
    font-size: 14px;
}

.case-info-sidebar .customize-btn {
    background: none;
    border: none;
    color: var(--primary-color);
    cursor: pointer;
    font-size: 12px;
    padding: 4px 8px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.case-info-sidebar .customize-btn:hover {
    background: var(--primary-color);
    color: white;
}

.case-info-sidebar .info-content {
    padding: 16px;
}

.case-info-sidebar .empty-state {
    color: var(--text-secondary);
    font-size: 14px;
    font-style: italic;
}

/* Assignees样式 */
.case-info-sidebar .assignee-item {
    display: flex;
    align-items: center;
    gap: 8px;
}

.case-info-sidebar .avatar {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    border: 1px solid var(--border-color);
}

.case-info-sidebar .assignee-item span {
    font-size: 14px;
    color: var(--text-primary);
}

/* Labels样式 */
.case-info-sidebar .label {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    margin-right: 6px;
    margin-bottom: 6px;
    color: white;
    text-decoration: none;
}

.case-info-sidebar .label-bug {
    background-color: #d73a49;
}

.case-info-sidebar .label-feature {
    background-color: #0366d6;
}

.case-info-sidebar .label-enhancement {
    background-color: #a2eeef;
    color: #000;
}

.case-info-sidebar .label-documentation {
    background-color: #0075ca;
}

.case-info-sidebar .label-question {
    background-color: #d876e3;
}

.case-info-sidebar .label-wontfix {
    background-color: #ffffff;
    color: #000;
}

.case-info-sidebar .label-duplicate {
    background-color: #cfd3d7;
    color: #000;
}

.case-info-sidebar .label-invalid {
    background-color: #e4e669;
    color: #000;
}

.case-info-sidebar .label-good-first-issue {
    background-color: #7057ff;
}

.case-info-sidebar .label-help-wanted {
    background-color: #008672;
}

.case-info-sidebar .label-default {
    background-color: #6c757d;
}

/* Projects和Milestone样式 */
.case-info-sidebar .project-item,
.case-info-sidebar .milestone-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px;
    background: var(--bg-secondary);
    border-radius: 6px;
    font-size: 14px;
    color: var(--text-primary);
}

.case-info-sidebar .project-item::before {
    content: "📁";
    font-size: 16px;
}

.case-info-sidebar .milestone-item::before {
    content: "🎯";
    font-size: 16px;
}

/* Development样式 */
.case-info-sidebar .dev-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px;
    background: var(--bg-secondary);
    border-radius: 6px;
    cursor: pointer;
    transition: background-color 0.2s ease;
    margin-bottom: 8px;
}

.case-info-sidebar .dev-item:hover {
    background: var(--border-color);
}

.case-info-sidebar .dev-item i:first-child {
    color: var(--primary-color);
}

.case-info-sidebar .dev-item span {
    flex: 1;
    font-size: 14px;
    color: var(--text-primary);
}

.case-info-sidebar .dev-item i:last-child {
    color: var(--text-secondary);
}

.case-info-sidebar .dev-link {
    font-size: 14px;
    color: var(--text-secondary);
}

.case-info-sidebar .dev-link a {
    color: var(--primary-color);
    text-decoration: none;
}

.case-info-sidebar .dev-link a:hover {
    text-decoration: underline;
}

/* Notifications样式 */
.case-info-sidebar .notification-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 14px;
    color: var(--text-primary);
    margin-bottom: 8px;
}

.case-info-sidebar .notification-btn:hover {
    background: var(--border-color);
}

.case-info-sidebar .notification-btn i {
    font-size: 14px;
}

.case-info-sidebar .notification-btn span {
    font-size: 14px;
}

.case-info-sidebar .notification-text {
    font-size: 12px;
    color: var(--text-secondary);
    line-height: 1.4;
}

/* Participants样式 */
.case-info-sidebar .participants-list {
    display: flex;
    gap: 4px;
    flex-wrap: wrap;
}

.case-info-sidebar .participants-list .avatar {
    width: 24px;
    height: 24px;
    cursor: pointer;
    transition: transform 0.2s ease;
}

.case-info-sidebar .participants-list .avatar:hover {
    transform: scale(1.1);
}

/* Actions样式 */
.case-info-sidebar .action-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px;
    cursor: pointer;
    transition: background-color 0.2s ease;
    border-radius: 4px;
}

.case-info-sidebar .action-item:last-child {
    border-bottom: none;
}

.case-info-sidebar .action-item:hover {
    background: var(--bg-secondary);
}

.case-info-sidebar .action-item i {
    font-size: 14px;
    color: var(--text-secondary);
    width: 16px;
}

.case-info-sidebar .action-item span {
    font-size: 14px;
    color: var(--text-primary);
}

/* 主布局调整 */
.cases-layout.with-case-info {
    grid-template-columns: 300px 1fr 1fr;
    padding-right: 420px; /* 为侧边栏留出空间 */
}

.cases-layout.with-case-info-collapsed {
    grid-template-columns: 300px 1fr 1fr;
    padding-right: 80px; /* 为折叠的侧边栏留出空间 */
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .case-info-sidebar {
        width: 350px;
    }
    
    .case-info-sidebar.collapsed {
        width: 50px;
    }
    
    .cases-layout.with-case-info {
        padding-right: 370px;
    }
    
    .cases-layout.with-case-info-collapsed {
        padding-right: 70px;
    }
}

@media (max-width: 768px) {
    .case-info-sidebar {
        width: 100%;
        right: -100%;
    }
    
    .case-info-sidebar.collapsed {
        width: 100%;
        right: -100%;
    }
    
    .cases-layout.with-case-info,
    .cases-layout.with-case-info-collapsed {
        padding-right: 0;
    }
}

/* 滚动条样式 */
.case-info-sidebar .sidebar-content::-webkit-scrollbar {
    width: 6px;
}

.case-info-sidebar .sidebar-content::-webkit-scrollbar-track {
    background: var(--bg-secondary);
}

.case-info-sidebar .sidebar-content::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 3px;
}

.case-info-sidebar .sidebar-content::-webkit-scrollbar-thumb:hover {
    background: var(--text-secondary);
} 