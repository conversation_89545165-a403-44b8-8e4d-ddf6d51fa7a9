# DN产品工作流节点产品文档

## 文档结构

本文件夹包含DN (Defect Navigation) 产品工作流节点系统的完整产品文档，包括需求分析、技术规范和开发指南。

### 📋 文档清单

#### 1. [workflow_nodes_overview.md](./workflow_nodes_overview.md)
**工作流节点概述**
- 项目背景和定位
- 核心节点类型详解
- 节点特性总结
- 技术架构特点
- 应用场景和发展方向

#### 2. [workflow_nodes_prd.md](./workflow_nodes_prd.md)
**产品需求文档 (PRD)**
- 产品概述和目标用户
- 详细功能需求规格
- 非功能需求和性能指标
- 用户体验设计原则
- 技术架构和开发计划
- 验收标准和风险评估

#### 3. [node_development_specification.md](./node_development_specification.md)
**节点开发技术规范**
- 节点架构设计标准
- 代码实现规范和示例
- 前端组件开发指南
- 数据流管理机制
- 性能优化策略
- 测试规范和部署流程

## 🎯 核心节点类型

基于产品文档分析，系统包含以下5个核心节点类型：

### 1. **Case Info** (Case基础信息)
- **类型**: 查询节点
- **功能**: 提供Case基础信息查询和展示
- **权限**: YE用户可编辑，Module用户只读
- **特性**: 支持参数联动和动态调整

### 2. **Scan Info** (Lot检测信息及Map图)
- **类型**: 查询+展示节点
- **功能**: Lot检测信息查询和Map图展示
- **权限**: YE用户可交互操作，Module用户只读
- **特性**: 垂直表格+图像联动展示

### 3. **Lot History** (Lot履历信息)
- **类型**: 查询+表格展示节点
- **功能**: Lot履历信息查询和展示
- **权限**: YE用户可筛选设备，Module用户只读
- **特性**: 时间序列数据展示

### 4. **Map Gallery** (Wafer Map图像画廊)
- **类型**: 展示节点
- **功能**: 所有Wafer的Map图和Image图展示
- **权限**: YE用户可选择和交互，Module用户只读
- **特性**: 网格布局+图像预览

### 5. **Trend Chart** (缺陷趋势图)
- **类型**: 查询+图表节点
- **功能**: 缺陷趋势分析和图表展示
- **权限**: YE用户可配置参数，Module用户只读
- **特性**: 多维度趋势分析+自定义图表

## 🏗️ 技术架构特点

### 权限模型
- **YE用户**: 完整操作权限（编辑、配置、交互）
- **Module用户**: 只读权限（查看结果）
- **Admin用户**: 超级管理员权限

### 数据流设计
- **输入标准化**: 统一的参数格式（Time, Product ID, Lot ID等）
- **联动展示**: 节点间数据自动传递和关联
- **动态参数**: 根据用户选择自动调整参数选项

### 组件化架构
- **模块化设计**: 每个节点独立封装
- **可扩展性**: 支持新增节点类型
- **标准化接口**: 统一的数据输入输出格式

## 📊 应用场景

### 典型工作流程
```
Case Info → Scan Info → Lot History → Map Gallery → Trend Chart
    ↓           ↓           ↓            ↓            ↓
基础信息    检测详情    履历分析    图像浏览    趋势分析
```

### 用户角色应用
- **YE工程师**: 完整的缺陷分析和诊断能力
- **Module用户**: 结果查看和基础操作
- **管理员**: 系统配置和权限管理

## 🚀 开发计划

### 第一阶段（4周）- 基础框架
- [ ] Case Info节点开发
- [ ] 权限系统实现
- [ ] 核心数据查询功能

### 第二阶段（6周）- 图像处理
- [ ] Scan Info节点开发
- [ ] 图像展示组件
- [ ] 数据联动功能

### 第三阶段（4周）- 数据展示
- [ ] Lot History节点开发
- [ ] 表格展示优化
- [ ] 数据导出功能

### 第四阶段（6周）- 图像画廊
- [ ] Map Gallery节点开发
- [ ] 图像画廊功能
- [ ] 性能优化

### 第五阶段（4周）- 趋势分析
- [ ] Trend Chart节点开发
- [ ] 图表组件集成
- [ ] 系统集成测试

## 📋 验收标准

### 功能验收
- ✅ 所有节点功能正常运行
- ✅ 权限控制正确实现
- ✅ 数据展示准确无误
- ✅ 用户交互流畅

### 性能验收
- ✅ 页面加载时间 < 3秒
- ✅ 数据查询响应时间 < 2秒
- ✅ 支持100+并发用户
- ✅ 系统稳定性 > 99.5%

### 质量验收
- ✅ 代码质量符合规范
- ✅ 测试覆盖率 > 80%
- ✅ 安全测试通过
- ✅ 用户体验测试通过

## 🔧 开发环境

### 技术栈
- **前端**: Vue 3 + JavaScript ES6+
- **后端**: Python Flask + SQLite
- **图表**: Chart.js
- **样式**: CSS3 + 响应式设计

### 开发工具
- **IDE**: VS Code / WebStorm
- **版本控制**: Git
- **测试**: Jest (前端) + pytest (后端)
- **构建**: Webpack / Vite

## 📞 联系方式

如有问题或建议，请联系：
- **产品经理**: [产品团队]
- **技术负责人**: [开发团队]
- **项目经理**: [项目团队]

---

*最后更新时间: 2024年1月* 