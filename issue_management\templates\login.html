<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DN System - 登录</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            background: linear-gradient(135deg, #0d1117 0%, #161b22 100%);
            color: #f0f6fc;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .login-container {
            background: #21262d;
            border: 1px solid #30363d;
            border-radius: 12px;
            padding: 40px;
            width: 100%;
            max-width: 400px;
            box-shadow: 0 16px 32px rgba(0, 0, 0, 0.3);
        }

        .login-header {
            text-align: center;
            margin-bottom: 32px;
        }

        .login-header h1 {
            color: #f0f6fc;
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .login-header .logo {
            font-size: 32px;
            color: #f85149;
            margin-bottom: 16px;
        }

        .login-header p {
            color: #7d8590;
            font-size: 14px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #f0f6fc;
            font-weight: 500;
            font-size: 14px;
        }

        .form-control {
            width: 100%;
            padding: 12px 16px;
            background: #0d1117;
            border: 1px solid #30363d;
            border-radius: 6px;
            color: #f0f6fc;
            font-size: 14px;
            transition: border-color 0.2s ease;
        }

        .form-control:focus {
            outline: none;
            border-color: #1f6feb;
            box-shadow: 0 0 0 3px rgba(31, 111, 235, 0.1);
        }

        .form-control::placeholder {
            color: #7d8590;
        }

        .btn {
            width: 100%;
            padding: 12px 16px;
            background: #238636;
            border: none;
            border-radius: 6px;
            color: #ffffff;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: background-color 0.2s ease;
            margin-top: 16px;
        }

        .btn:hover {
            background: #2ea043;
        }

        .btn:disabled {
            background: #30363d;
            cursor: not-allowed;
        }

        .error-message {
            background: #da3633;
            color: #ffffff;
            padding: 12px 16px;
            border-radius: 6px;
            margin-bottom: 16px;
            font-size: 14px;
            display: none;
        }

        .success-message {
            background: #238636;
            color: #ffffff;
            padding: 12px 16px;
            border-radius: 6px;
            margin-bottom: 16px;
            font-size: 14px;
            display: none;
        }

        .demo-accounts {
            margin-top: 24px;
            padding: 16px;
            background: #161b22;
            border-radius: 6px;
            border: 1px solid #30363d;
        }

        .demo-accounts h3 {
            color: #f0f6fc;
            font-size: 14px;
            margin-bottom: 12px;
        }

        .demo-account {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #30363d;
            font-size: 12px;
        }

        .demo-account:last-child {
            border-bottom: none;
        }

        .demo-account .username {
            color: #f0f6fc;
            font-weight: 500;
        }

        .demo-account .role {
            color: #7d8590;
        }

        .demo-account .password {
            color: #58a6ff;
            font-family: monospace;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }

        .loading i {
            font-size: 24px;
            color: #1f6feb;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <div class="logo">
                <i class="fas fa-bug"></i>
            </div>
            <h1>DN System</h1>
            <p>请登录您的账户</p>
        </div>

        <div class="error-message" id="errorMessage"></div>
        <div class="success-message" id="successMessage"></div>

        <form id="loginForm">
            <div class="form-group">
                <label for="username">用户名</label>
                <input type="text" id="username" class="form-control" placeholder="请输入用户名" required>
            </div>

            <div class="form-group">
                <label for="password">密码</label>
                <input type="password" id="password" class="form-control" placeholder="请输入密码" required>
            </div>

            <button type="submit" class="btn" id="loginBtn">
                <i class="fas fa-sign-in-alt"></i> 登录
            </button>
        </form>

        <div class="loading" id="loading">
            <i class="fas fa-spinner"></i>
            <p>正在登录...</p>
        </div>

        <div class="demo-accounts">
            <h3><i class="fas fa-info-circle"></i> 演示账户</h3>
            <div class="demo-account">
                <span class="username">admin</span>
                <span class="role">管理员 (admin module)</span>
                <span class="password">admin123</span>
            </div>
            <div class="demo-account">
                <span class="username">ye_user</span>
                <span class="role">YE用户 (ye module)</span>
                <span class="password">ye123</span>
            </div>
            <div class="demo-account">
                <span class="username">admin_user</span>
                <span class="role">管理用户 (admin module)</span>
                <span class="password">admin123</span>
            </div>
            <div class="demo-account">
                <span class="username">zhang_san</span>
                <span class="role">QA用户 (ye module)</span>
                <span class="password">zhang123</span>
            </div>
            <div class="demo-account">
                <span class="username">zhao_liu</span>
                <span class="role">QA经理 (admin module)</span>
                <span class="password">zhao123</span>
            </div>
        </div>
    </div>

    <script>
        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const errorMessage = document.getElementById('errorMessage');
            const successMessage = document.getElementById('successMessage');
            const loading = document.getElementById('loading');
            const loginBtn = document.getElementById('loginBtn');
            
            // 隐藏之前的消息
            errorMessage.style.display = 'none';
            successMessage.style.display = 'none';
            
            // 显示加载状态
            loading.style.display = 'block';
            loginBtn.disabled = true;
            
            try {
                const response = await fetch('/api/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username, password })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    successMessage.textContent = '登录成功，正在跳转...';
                    successMessage.style.display = 'block';
                    
                    // 延迟跳转到主页
                    setTimeout(() => {
                        window.location.href = '/';
                    }, 1000);
                } else {
                    errorMessage.textContent = data.error || '登录失败';
                    errorMessage.style.display = 'block';
                }
            } catch (error) {
                errorMessage.textContent = '网络错误，请重试';
                errorMessage.style.display = 'block';
            } finally {
                loading.style.display = 'none';
                loginBtn.disabled = false;
            }
        });

        // 点击演示账户自动填充
        document.querySelectorAll('.demo-account').forEach(account => {
            account.addEventListener('click', function() {
                const username = this.querySelector('.username').textContent;
                const password = this.querySelector('.password').textContent;
                
                document.getElementById('username').value = username;
                document.getElementById('password').value = password;
            });
        });
    </script>
</body>
</html> 