<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试连接线</title>
    <style>
        body {
            background: #0d1117;
            color: #f0f6fc;
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
        }
        
        .container {
            position: relative;
            width: 800px;
            height: 600px;
            border: 1px solid #30363d;
            margin: 20px auto;
        }
        
        .node {
            position: absolute;
            width: 120px;
            height: 80px;
            background: #21262d;
            border: 2px solid #30363d;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            cursor: pointer;
        }
        
        .node.completed {
            border-color: #238636;
        }
        
        .node.pending {
            border-color: #ff8c00;
        }
        
        svg {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1;
            pointer-events: none;
        }
        
        .nodes-container {
            position: relative;
            z-index: 2;
        }
    </style>
</head>
<body>
    <h1>连接线测试页面</h1>
    <div class="container" id="container">
        <svg id="svg"></svg>
        <div class="nodes-container">
            <div class="node completed" id="node-1" style="left: 50px; top: 50px;">HL<br/>查询</div>
            <div class="node pending" id="node-2" style="left: 250px; top: 50px;">HD<br/>展示</div>
            <div class="node pending" id="node-3" style="left: 450px; top: 50px;">CI<br/>信息</div>
            <div class="node pending" id="node-4" style="left: 150px; top: 200px;">SI<br/>查询</div>
            <div class="node pending" id="node-5" style="left: 350px; top: 200px;">LH<br/>查询</div>
        </div>
    </div>
    
    <button onclick="drawConnections()">绘制连接线</button>
    <button onclick="clearConnections()">清除连接线</button>
    
    <script>
        function drawConnections() {
            const svg = document.getElementById('svg');
            const container = document.getElementById('container');
            
            // 清除现有连接线
            svg.innerHTML = '';
            
            // 创建defs元素用于箭头
            const defs = document.createElementNS('http://www.w3.org/2000/svg', 'defs');
            const marker = document.createElementNS('http://www.w3.org/2000/svg', 'marker');
            marker.setAttribute('id', 'arrowhead');
            marker.setAttribute('viewBox', '0 0 10 10');
            marker.setAttribute('refX', '9');
            marker.setAttribute('refY', '3');
            marker.setAttribute('markerWidth', '6');
            marker.setAttribute('markerHeight', '6');
            marker.setAttribute('orient', 'auto');
            
            const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');
            path.setAttribute('d', 'M0,0 L0,6 L9,3 z');
            path.setAttribute('fill', '#1f6feb');
            
            marker.appendChild(path);
            defs.appendChild(marker);
            svg.appendChild(defs);
            
            // 定义连接关系
            const connections = [
                {from: 'node-1', to: 'node-2'},
                {from: 'node-2', to: 'node-3'},
                {from: 'node-3', to: 'node-4'},
                {from: 'node-3', to: 'node-5'}
            ];
            
            // 绘制连接线
            connections.forEach(conn => {
                const fromElement = document.getElementById(conn.from);
                const toElement = document.getElementById(conn.to);
                
                if (fromElement && toElement) {
                    const fromRect = fromElement.getBoundingClientRect();
                    const toRect = toElement.getBoundingClientRect();
                    const containerRect = container.getBoundingClientRect();
                    
                    const fromX = fromRect.left - containerRect.left + fromRect.width / 2;
                    const fromY = fromRect.top - containerRect.top + fromRect.height / 2;
                    const toX = toRect.left - containerRect.left + toRect.width / 2;
                    const toY = toRect.top - containerRect.top + toRect.height / 2;
                    
                    const line = document.createElementNS('http://www.w3.org/2000/svg', 'line');
                    line.setAttribute('x1', fromX);
                    line.setAttribute('y1', fromY);
                    line.setAttribute('x2', toX);
                    line.setAttribute('y2', toY);
                    line.setAttribute('stroke', '#1f6feb');
                    line.setAttribute('stroke-width', '3');
                    line.setAttribute('marker-end', 'url(#arrowhead)');
                    
                    svg.appendChild(line);
                    
                    console.log(`绘制连接线: ${conn.from} -> ${conn.to} (${fromX},${fromY}) -> (${toX},${toY})`);
                }
            });
        }
        
        function clearConnections() {
            const svg = document.getElementById('svg');
            svg.innerHTML = '';
        }
        
        // 页面加载完成后自动绘制连接线
        window.addEventListener('load', () => {
            setTimeout(drawConnections, 100);
        });
    </script>
</body>
</html>
