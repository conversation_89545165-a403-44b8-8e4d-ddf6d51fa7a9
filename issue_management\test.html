<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>DN System 测试页面</h1>
        <div id="status" class="status">正在测试...</div>
        <div id="issues-data"></div>
        <div id="workflows-data"></div>
    </div>

    <script>
        async function testAPI() {
            const statusDiv = document.getElementById('status');
            const issuesDiv = document.getElementById('issues-data');
            const workflowsDiv = document.getElementById('workflows-data');
            
            try {
                // 测试Issues API
                const issuesResponse = await fetch('/api/issues');
                const issuesData = await issuesResponse.json();
                
                issuesDiv.innerHTML = `
                    <h3>Issues API 测试结果:</h3>
                    <pre>${JSON.stringify(issuesData, null, 2)}</pre>
                `;
                
                // 测试Workflows API
                const workflowsResponse = await fetch('/api/workflows');
                const workflowsData = await workflowsResponse.json();
                
                workflowsDiv.innerHTML = `
                    <h3>Workflows API 测试结果:</h3>
                    <pre>${JSON.stringify(workflowsData, null, 2)}</pre>
                `;
                
                statusDiv.className = 'status success';
                statusDiv.textContent = '✅ API测试成功！';
                
            } catch (error) {
                statusDiv.className = 'status error';
                statusDiv.textContent = '❌ API测试失败: ' + error.message;
            }
        }
        
        // 页面加载后测试
        document.addEventListener('DOMContentLoaded', testAPI);
    </script>
</body>
</html> 