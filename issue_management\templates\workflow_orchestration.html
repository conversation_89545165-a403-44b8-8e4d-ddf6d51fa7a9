<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DN产品工作流编排</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #0d1117;
            color: #e6edf3;
            line-height: 1.6;
        }

        .header {
            background: #161b22;
            border-bottom: 1px solid #30363d;
            padding: 1rem 2rem;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header h1 {
            color: #f0f6fc;
            font-size: 1.5rem;
            font-weight: 600;
        }

        .header .subtitle {
            color: #8b949e;
            font-size: 0.875rem;
            margin-top: 0.25rem;
        }

        .main-container {
            display: flex;
            height: calc(100vh - 80px);
        }

        .sidebar {
            width: 280px;
            background: #161b22;
            border-right: 1px solid #30363d;
            padding: 1rem;
            overflow-y: auto;
        }

        .sidebar h3 {
            color: #f0f6fc;
            font-size: 1rem;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid #30363d;
        }

        .node-palette {
            margin-bottom: 2rem;
        }

        .node-category {
            margin-bottom: 1.5rem;
        }

        .node-category-title {
            color: #8b949e;
            font-size: 0.875rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .node-item {
            background: #21262d;
            border: 1px solid #30363d;
            border-radius: 6px;
            padding: 0.75rem;
            margin-bottom: 0.5rem;
            cursor: grab;
            transition: all 0.2s ease;
        }

        .node-item:hover {
            background: #30363d;
            border-color: #58a6ff;
        }

        .node-item:active {
            cursor: grabbing;
        }

        .node-item .node-icon {
            display: inline-block;
            width: 32px;
            height: 32px;
            background: #1f6feb;
            border-radius: 6px;
            text-align: center;
            line-height: 32px;
            color: white;
            font-weight: 600;
            margin-right: 0.75rem;
            vertical-align: middle;
        }

        .node-item .node-info {
            display: inline-block;
            vertical-align: middle;
        }

        .node-item .node-title {
            color: #f0f6fc;
            font-weight: 500;
            font-size: 0.875rem;
        }

        .node-item .node-desc {
            color: #8b949e;
            font-size: 0.75rem;
            margin-top: 0.25rem;
        }

        .canvas-container {
            flex: 1;
            position: relative;
            background: #0d1117;
            overflow: hidden;
        }

        .canvas {
            width: 100%;
            height: 100%;
            position: relative;
            background-image: 
                radial-gradient(circle at 1px 1px, rgba(255,255,255,0.1) 1px, transparent 0);
            background-size: 20px 20px;
        }

        .workflow-node {
            position: absolute;
            background: #161b22;
            border: 2px solid #30363d;
            border-radius: 8px;
            padding: 1rem;
            min-width: 200px;
            cursor: move;
            transition: all 0.2s ease;
            user-select: none;
        }

        .workflow-node:hover {
            border-color: #58a6ff;
            box-shadow: 0 0 0 2px rgba(88, 166, 255, 0.2);
        }

        .workflow-node.selected {
            border-color: #58a6ff;
            box-shadow: 0 0 0 2px rgba(88, 166, 255, 0.3);
        }

        .workflow-node.query { border-color: #238636; }
        .workflow-node.algorithm { border-color: #da3633; }
        .workflow-node.processing { border-color: #f85149; }
        .workflow-node.display { border-color: #1f6feb; }

        .node-header {
            display: flex;
            align-items: center;
            margin-bottom: 0.75rem;
        }

        .node-header .node-icon {
            width: 40px;
            height: 40px;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            margin-right: 0.75rem;
        }

        .node-header .node-title {
            color: #f0f6fc;
            font-weight: 600;
            font-size: 1rem;
        }

        .node-content {
            color: #8b949e;
            font-size: 0.875rem;
            line-height: 1.4;
        }

        .node-ports {
            display: flex;
            justify-content: space-between;
            margin-top: 0.75rem;
        }

        .node-port {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #30363d;
            border: 2px solid #58a6ff;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .node-port:hover {
            background: #58a6ff;
            transform: scale(1.2);
        }

        .node-port.input {
            margin-left: -6px;
        }

        .node-port.output {
            margin-right: -6px;
        }

        .connection-line {
            position: absolute;
            pointer-events: none;
            z-index: 1;
        }

        .connection-path {
            stroke: #58a6ff;
            stroke-width: 2;
            fill: none;
            stroke-dasharray: 5,5;
            animation: dash 1s linear infinite;
        }

        @keyframes dash {
            to {
                stroke-dashoffset: -10;
            }
        }

        .toolbar {
            position: absolute;
            top: 1rem;
            right: 1rem;
            display: flex;
            gap: 0.5rem;
            z-index: 10;
        }

        .toolbar button {
            background: #21262d;
            border: 1px solid #30363d;
            color: #f0f6fc;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .toolbar button:hover {
            background: #30363d;
            border-color: #58a6ff;
        }

        .status-bar {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: #161b22;
            border-top: 1px solid #30363d;
            padding: 0.5rem 1rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.875rem;
            color: #8b949e;
        }

        .workflow-info {
            background: #21262d;
            border: 1px solid #30363d;
            border-radius: 6px;
            padding: 1rem;
            margin-bottom: 1rem;
        }

        .workflow-info h4 {
            color: #f0f6fc;
            margin-bottom: 0.5rem;
        }

        .workflow-info p {
            color: #8b949e;
            font-size: 0.875rem;
        }

        .empty-state {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            color: #8b949e;
        }

        .empty-state i {
            font-size: 4rem;
            margin-bottom: 1rem;
            color: #30363d;
        }

        .empty-state h3 {
            color: #f0f6fc;
            margin-bottom: 0.5rem;
        }

        .loading-state {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            color: #8b949e;
        }

        .loading-state i {
            font-size: 3rem;
            margin-bottom: 1rem;
            color: #58a6ff;
        }

        .loading-state h3 {
            color: #f0f6fc;
            margin-bottom: 0.5rem;
        }

        .error-state {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            color: #8b949e;
        }

        .error-state i {
            font-size: 3rem;
            margin-bottom: 1rem;
            color: #f85149;
        }

        .error-state h3 {
            color: #f0f6fc;
            margin-bottom: 0.5rem;
        }

        .btn-primary {
            background: #238636;
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.875rem;
            margin-top: 1rem;
            transition: background 0.2s ease;
        }

        .btn-primary:hover {
            background: #2ea043;
        }

        .node-status {
            margin-top: 0.5rem;
        }

        .status-badge {
            display: inline-block;
            padding: 0.25rem 0.5rem;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .status-badge.completed {
            background: rgba(35, 134, 54, 0.2);
            color: #3fb950;
        }

        .status-badge.pending {
            background: rgba(187, 128, 9, 0.2);
            color: #d29922;
        }

        .status-badge.running {
            background: rgba(88, 166, 255, 0.2);
            color: #58a6ff;
        }

        .status-badge.error {
            background: rgba(248, 81, 73, 0.2);
            color: #f85149;
        }

        .node-ports {
            margin-top: 1rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .node-port {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #30363d;
            border: 2px solid #58a6ff;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .node-port:hover {
            background: #58a6ff;
            transform: scale(1.2);
        }

        .node-port.input {
            margin-right: auto;
        }

        .node-port.output {
            margin-left: auto;
        }

        .connection-line {
            z-index: 1;
        }

        .connection-path {
            stroke: #58a6ff;
            stroke-width: 2;
            fill: none;
            stroke-dasharray: 5,5;
            animation: dash 1s linear infinite;
        }

        @keyframes dash {
            to {
                stroke-dashoffset: -10;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1><i class="fas fa-project-diagram"></i> DN产品工作流编排</h1>
        <p class="subtitle">基于通用节点架构的工作流可视化编排系统</p>
    </div>

    <div class="main-container">
        <div class="sidebar">
            <div class="workflow-info">
                <h4>当前工作流</h4>
                <p>缺陷分析工作流 - 完整的半导体缺陷检测与分析流程</p>
            </div>

            <div class="node-palette">
                <h3>节点面板</h3>
                
                <div class="node-category">
                    <div class="node-category-title">查询节点 (Query)</div>
                    <div class="node-item" draggable="true" data-node-type="query" data-node-config="case-info">
                        <div class="node-icon" style="background: #238636;">CI</div>
                        <div class="node-info">
                            <div class="node-title">Case Info 查询</div>
                            <div class="node-desc">查询基础Case信息</div>
                        </div>
                    </div>
                    <div class="node-item" draggable="true" data-node-type="query" data-node-config="hold-lot">
                        <div class="node-icon" style="background: #238636;">HL</div>
                        <div class="node-info">
                            <div class="node-title">Hold Lot 查询</div>
                            <div class="node-desc">查询Hold状态Lot列表</div>
                        </div>
                    </div>
                    <div class="node-item" draggable="true" data-node-type="query" data-node-config="lot-history">
                        <div class="node-icon" style="background: #238636;">LH</div>
                        <div class="node-info">
                            <div class="node-title">Lot History 查询</div>
                            <div class="node-desc">查询Lot历史处理信息</div>
                        </div>
                    </div>
                </div>

                <div class="node-category">
                    <div class="node-category-title">算法节点 (Algorithm)</div>
                    <div class="node-item" draggable="true" data-node-type="algorithm" data-node-config="robot-analysis">
                        <div class="node-icon" style="background: #da3633;">RA</div>
                        <div class="node-info">
                            <div class="node-title">Robot Analysis</div>
                            <div class="node-desc">规则算法分析</div>
                        </div>
                    </div>
                    <div class="node-item" draggable="true" data-node-type="algorithm" data-node-config="tool-commonality">
                        <div class="node-icon" style="background: #da3633;">TC</div>
                        <div class="node-info">
                            <div class="node-title">Tool Commonality</div>
                            <div class="node-desc">机台相关性分析</div>
                        </div>
                    </div>
                </div>

                <div class="node-category">
                    <div class="node-category-title">处理节点 (Processing)</div>
                    <div class="node-item" draggable="true" data-node-type="processing" data-node-config="defect-map">
                        <div class="node-icon" style="background: #f85149;">DM</div>
                        <div class="node-info">
                            <div class="node-title">Defect Map 生成</div>
                            <div class="node-desc">生成缺陷热力图</div>
                        </div>
                    </div>
                    <div class="node-item" draggable="true" data-node-type="processing" data-node-config="trend-data">
                        <div class="node-icon" style="background: #f85149;">TD</div>
                        <div class="node-info">
                            <div class="node-title">Trend 数据处理</div>
                            <div class="node-desc">生成趋势分析数据</div>
                        </div>
                    </div>
                </div>

                <div class="node-category">
                    <div class="node-category-title">展示节点 (Display)</div>
                    <div class="node-item" draggable="true" data-node-type="display" data-node-config="map-display">
                        <div class="node-icon" style="background: #1f6feb;">MD</div>
                        <div class="node-info">
                            <div class="node-title">Map 展示</div>
                            <div class="node-desc">交互式地图展示</div>
                        </div>
                    </div>
                    <div class="node-item" draggable="true" data-node-type="display" data-node-config="chart-display">
                        <div class="node-icon" style="background: #1f6feb;">CD</div>
                        <div class="node-info">
                            <div class="node-title">Chart 展示</div>
                            <div class="node-desc">图表趋势展示</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="canvas-container">
            <div class="toolbar">
                <button onclick="clearCanvas()">
                    <i class="fas fa-trash"></i> 清空
                </button>
                <button onclick="saveWorkflow()">
                    <i class="fas fa-save"></i> 保存
                </button>
                <button onclick="loadWorkflow()">
                    <i class="fas fa-folder-open"></i> 加载
                </button>
                <button onclick="executeWorkflow()">
                    <i class="fas fa-play"></i> 执行
                </button>
            </div>

            <div class="canvas" id="canvas">
                <div class="empty-state">
                    <i class="fas fa-project-diagram"></i>
                    <h3>开始构建工作流</h3>
                    <p>从左侧面板拖拽节点到此区域开始构建您的工作流</p>
                </div>
            </div>

            <div class="status-bar">
                <div class="status-left">
                    <span>节点数量: <span id="nodeCount">0</span></span>
                    <span style="margin-left: 1rem;">连接数量: <span id="connectionCount">0</span></span>
                </div>
                <div class="status-right">
                    <span>状态: <span id="workflowStatus">就绪</span></span>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 工作流编排器
        class WorkflowOrchestrator {
            constructor() {
                this.nodes = new Map();
                this.connections = new Map();
                this.selectedNode = null;
                this.draggedNode = null;
                this.connectionStart = null;
                this.nodeCounter = 0;
                this.workflowData = null;
                
                this.init();
            }

            async init() {
                this.setupEventListeners();
                this.setupDragAndDrop();
                this.updateStats();
                
                // 显示加载状态
                document.getElementById('canvas').innerHTML = `
                    <div class="loading-state">
                        <i class="fas fa-spinner fa-spin"></i>
                        <h3>正在加载工作流数据...</h3>
                        <p>请稍候</p>
                    </div>
                `;
                
                // 从后端加载工作流数据
                await this.loadWorkflowFromAPI();
            }

            async loadWorkflowFromAPI() {
                try {
                    console.log('正在从API加载工作流数据...');
                    
                    // 获取工作流列表
                    const response = await fetch('/api/workflows');
                    const data = await response.json();
                    
                    console.log('工作流数据:', data);
                    
                    // 查找dn-production工作流
                    const productionWorkflow = data.workflows.find(w => w.id === 'dn-production');
                    
                    if (productionWorkflow && productionWorkflow.config && productionWorkflow.config.nodes) {
                        this.workflowData = productionWorkflow.config;
                        console.log('找到dn-production工作流，节点数量:', this.workflowData.nodes.length);
                        
                        // 清空画布
                        document.getElementById('canvas').innerHTML = '';
                        
                        // 渲染节点
                        await this.renderWorkflowNodes();
                        
                        // 渲染连接
                        setTimeout(() => {
                            this.renderWorkflowConnections();
                        }, 500);
                        
                    } else {
                        console.warn('未找到dn-production工作流或节点数据');
                        this.showEmptyState();
                    }
                    
                } catch (error) {
                    console.error('加载工作流数据失败:', error);
                    this.showErrorState();
                }
            }

            async renderWorkflowNodes() {
                if (!this.workflowData || !this.workflowData.nodes) return;
                
                for (const nodeData of this.workflowData.nodes) {
                    const nodeElement = this.createWorkflowNode(
                        nodeData.type,
                        nodeData.id,
                        nodeData.position.x,
                        nodeData.position.y,
                        nodeData
                    );
                    
                    // 添加到nodes集合
                    this.nodes.set(nodeData.id, {
                        id: nodeData.id,
                        type: nodeData.type,
                        config: nodeData.id,
                        x: nodeData.position.x,
                        y: nodeData.position.y,
                        element: nodeElement,
                        data: nodeData
                    });
                }
                
                this.updateStats();
            }

            renderWorkflowConnections() {
                if (!this.workflowData || !this.workflowData.connections) return;
                
                for (const connectionData of this.workflowData.connections) {
                    const fromNode = this.nodes.get(connectionData.from);
                    const toNode = this.nodes.get(connectionData.to);
                    
                    if (fromNode && toNode) {
                        const connectionId = `${connectionData.from}-${connectionData.to}`;
                        this.connections.set(connectionId, {
                            id: connectionId,
                            from: connectionData.from,
                            to: connectionData.to,
                            fromPort: 'output',
                            toPort: 'input'
                        });
                    }
                }
                
                this.updateConnections();
            }

            createWorkflowNode(type, nodeId, x, y, nodeData) {
                const node = document.createElement('div');
                node.className = `workflow-node ${type}`;
                node.dataset.nodeId = nodeId;
                node.style.left = x + 'px';
                node.style.top = y + 'px';

                // 根据节点类型设置图标背景色
                const iconColors = {
                    query: '#238636',
                    algorithm: '#da3633', 
                    processing: '#f85149',
                    display: '#1f6feb'
                };

                const iconColor = iconColors[type] || '#1f6feb';
                
                node.innerHTML = `
                    <div class="node-header">
                        <div class="node-icon" style="background-color: ${iconColor};">
                            ${nodeData.icon || type.charAt(0).toUpperCase()}
                        </div>
                        <div class="node-title">${nodeData.title || nodeId}</div>
                    </div>
                    <div class="node-content">
                        <p>${nodeData.description || '节点描述'}</p>
                        <div class="node-status">
                            <span class="status-badge ${nodeData.status || 'pending'}">${this.getStatusText(nodeData.status)}</span>
                        </div>
                    </div>
                    <div class="node-ports">
                        ${nodeData.inputs && nodeData.inputs.length > 0 ? '<div class="node-port input" title="输入端口"></div>' : ''}
                        ${nodeData.outputs && nodeData.outputs.length > 0 ? '<div class="node-port output" title="输出端口"></div>' : ''}
                    </div>
                `;

                document.getElementById('canvas').appendChild(node);
                this.setupNodeEvents(node);
                
                return node;
            }

            getStatusText(status) {
                const statusMap = {
                    'completed': '已完成',
                    'pending': '待执行',
                    'running': '执行中',
                    'error': '错误'
                };
                return statusMap[status] || '未知';
            }

            showEmptyState() {
                document.getElementById('canvas').innerHTML = `
                    <div class="empty-state">
                        <i class="fas fa-project-diagram"></i>
                        <h3>暂无工作流数据</h3>
                        <p>请检查后端数据或从左侧面板拖拽节点开始构建工作流</p>
                        <button onclick="loadWorkflow()" class="btn-primary">
                            <i class="fas fa-refresh"></i> 重新加载
                        </button>
                    </div>
                `;
            }

            showErrorState() {
                document.getElementById('canvas').innerHTML = `
                    <div class="error-state">
                        <i class="fas fa-exclamation-triangle"></i>
                        <h3>加载失败</h3>
                        <p>无法从服务器加载工作流数据，请检查网络连接</p>
                        <button onclick="loadWorkflow()" class="btn-primary">
                            <i class="fas fa-refresh"></i> 重试
                        </button>
                    </div>
                `;
            }

            setupEventListeners() {
                const canvas = document.getElementById('canvas');
                
                // 画布点击事件
                canvas.addEventListener('click', (e) => {
                    if (e.target === canvas) {
                        this.selectedNode = null;
                        document.querySelectorAll('.workflow-node').forEach(node => {
                            node.classList.remove('selected');
                        });
                    }
                });

                // 键盘事件
                document.addEventListener('keydown', (e) => {
                    if (e.key === 'Delete' && this.selectedNode) {
                        this.deleteNode(this.selectedNode);
                    }
                });
            }

            setupDragAndDrop() {
                const canvas = document.getElementById('canvas');
                
                canvas.addEventListener('mousedown', (e) => {
                    if (e.target.classList.contains('node-item')) {
                        const node = e.target.closest('.workflow-node');
                        if (node) {
                            this.startDrag(node, e);
                        }
                    }
                });
            }

            setupNodeEvents(node) {
                // 节点点击选择
                node.addEventListener('click', (e) => {
                    e.stopPropagation();
                    this.selectNode(node);
                });

                // 节点拖拽
                node.addEventListener('mousedown', (e) => {
                    if (e.button === 0) { // 左键
                        this.startDrag(node, e);
                    }
                });

                // 端口连接事件
                const ports = node.querySelectorAll('.node-port');
                ports.forEach(port => {
                    port.addEventListener('mousedown', (e) => {
                        e.stopPropagation();
                        this.startConnection(node, port);
                    });
                });
            }

            selectNode(node) {
                // 清除之前的选择
                document.querySelectorAll('.workflow-node').forEach(n => {
                    n.classList.remove('selected');
                });
                
                // 选择当前节点
                node.classList.add('selected');
                this.selectedNode = node.dataset.nodeId;
            }

            startDrag(node, e) {
                e.preventDefault();
                this.draggedNode = node;
                
                const rect = node.getBoundingClientRect();
                const canvasRect = document.getElementById('canvas').getBoundingClientRect();
                
                this.dragOffset = {
                    x: e.clientX - rect.left,
                    y: e.clientY - rect.top
                };

                const mouseMoveHandler = (e) => {
                    if (this.draggedNode) {
                        const newX = e.clientX - canvasRect.left - this.dragOffset.x;
                        const newY = e.clientY - canvasRect.top - this.dragOffset.y;
                        
                        this.draggedNode.style.left = Math.max(0, newX) + 'px';
                        this.draggedNode.style.top = Math.max(0, newY) + 'px';
                        
                        // 更新节点位置数据
                        const nodeId = this.draggedNode.dataset.nodeId;
                        const nodeData = this.nodes.get(nodeId);
                        if (nodeData) {
                            nodeData.x = newX;
                            nodeData.y = newY;
                        }
                        
                        // 更新连接线
                        this.updateConnections();
                    }
                };

                const mouseUpHandler = () => {
                    this.draggedNode = null;
                    document.removeEventListener('mousemove', mouseMoveHandler);
                    document.removeEventListener('mouseup', mouseUpHandler);
                };

                document.addEventListener('mousemove', mouseMoveHandler);
                document.addEventListener('mouseup', mouseUpHandler);
            }

            startConnection(node, port) {
                this.connectionStart = {
                    node: node,
                    port: port,
                    type: port.classList.contains('input') ? 'input' : 'output'
                };
                
                // 添加连接模式的视觉反馈
                document.body.style.cursor = 'crosshair';
                
                const mouseUpHandler = (e) => {
                    const targetPort = e.target.closest('.node-port');
                    const targetNode = e.target.closest('.workflow-node');
                    
                    if (targetPort && targetNode && targetNode !== node) {
                        const targetType = targetPort.classList.contains('input') ? 'input' : 'output';
                        
                        // 只允许从输出连接到输入
                        if (this.connectionStart.type === 'output' && targetType === 'input') {
                            this.createConnection(
                                this.connectionStart,
                                { node: targetNode, port: targetPort, type: targetType }
                            );
                        }
                    }
                    
                    this.connectionStart = null;
                    document.body.style.cursor = 'default';
                    document.removeEventListener('mouseup', mouseUpHandler);
                };
                
                document.addEventListener('mouseup', mouseUpHandler);
            }

            createConnection(start, end) {
                // 验证连接是否有效
                if (start.type === end.type) return; // 相同类型端口不能连接
                if (start.node === end.node) return; // 不能连接到自己

                const connectionId = `${start.node.dataset.nodeId}-${end.node.dataset.nodeId}`;
                
                if (this.connections.has(connectionId)) return; // 已存在连接

                // 创建连接
                this.connections.set(connectionId, {
                    id: connectionId,
                    from: start.node.dataset.nodeId,
                    to: end.node.dataset.nodeId,
                    fromPort: start.port,
                    toPort: end.port
                });

                this.updateConnections();
                this.updateStats();
            }

            updateConnections() {
                // 清除现有连接线
                document.querySelectorAll('.connection-line').forEach(line => line.remove());

                // 重新绘制所有连接
                this.connections.forEach(connection => {
                    this.drawConnection(connection);
                });
            }

            drawConnection(connection) {
                const fromNode = this.nodes.get(connection.from);
                const toNode = this.nodes.get(connection.to);

                if (!fromNode || !toNode) return;

                const fromRect = fromNode.element.getBoundingClientRect();
                const toRect = toNode.element.getBoundingClientRect();
                const canvasRect = document.getElementById('canvas').getBoundingClientRect();

                const fromX = fromRect.right - canvasRect.left;
                const fromY = fromRect.top + fromRect.height / 2 - canvasRect.top;
                const toX = toRect.left - canvasRect.left;
                const toY = toRect.top + toRect.height / 2 - canvasRect.top;

                const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
                svg.className = 'connection-line';
                svg.style.cssText = `
                    position: absolute;
                    left: 0;
                    top: 0;
                    width: 100%;
                    height: 100%;
                    pointer-events: none;
                    z-index: 1;
                `;

                const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');
                const midX = (fromX + toX) / 2;
                const d = `M ${fromX} ${fromY} C ${midX} ${fromY}, ${midX} ${toY}, ${toX} ${toY}`;
                
                path.setAttribute('d', d);
                path.className = 'connection-path';

                svg.appendChild(path);
                document.getElementById('canvas').appendChild(svg);
            }

            updateStats() {
                document.getElementById('nodeCount').textContent = this.nodes.size;
                document.getElementById('connectionCount').textContent = this.connections.size;
            }

            loadDefaultWorkflow() {
                // 重新从API加载工作流
                this.loadWorkflowFromAPI();
            }

            async saveWorkflow() {
                if (!this.workflowData) {
                    alert('没有工作流数据可保存');
                    return;
                }

                const workflowData = {
                    nodes: Array.from(this.nodes.values()).map(node => ({
                        id: node.id,
                        title: node.data.title,
                        type: node.type,
                        icon: node.data.icon,
                        description: node.data.description,
                        position: { x: node.x, y: node.y },
                        inputs: node.data.inputs || [],
                        outputs: node.data.outputs || [],
                        config: node.data.config || {},
                        status: node.data.status || 'pending',
                        permissions: node.data.permissions || ['ye', 'admin']
                    })),
                    connections: Array.from(this.connections.values()).map(conn => ({
                        from: conn.from,
                        to: conn.to,
                        status: 'active'
                    }))
                };

                try {
                    document.getElementById('workflowStatus').textContent = '保存中...';
                    
                    const response = await fetch('/api/workflows/dn-production/nodes', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(workflowData)
                    });

                    if (response.ok) {
                        const result = await response.json();
                        console.log('工作流保存成功:', result);
                        document.getElementById('workflowStatus').textContent = '已保存';
                        setTimeout(() => {
                            document.getElementById('workflowStatus').textContent = '就绪';
                        }, 2000);
                    } else {
                        throw new Error('保存失败');
                    }
                } catch (error) {
                    console.error('保存工作流失败:', error);
                    document.getElementById('workflowStatus').textContent = '保存失败';
                    setTimeout(() => {
                        document.getElementById('workflowStatus').textContent = '就绪';
                    }, 2000);
                }
            }

            executeWorkflow() {
                document.getElementById('workflowStatus').textContent = '执行中...';
                
                // 模拟工作流执行
                setTimeout(() => {
                    document.getElementById('workflowStatus').textContent = '执行完成';
                    setTimeout(() => {
                        document.getElementById('workflowStatus').textContent = '就绪';
                    }, 3000);
                }, 2000);
            }

            deleteNode(nodeId) {
                if (confirm('确定要删除这个节点吗？')) {
                    const node = this.nodes.get(nodeId);
                    if (node) {
                        // 删除相关连接
                        const connectionsToDelete = [];
                        this.connections.forEach((connection, id) => {
                            if (connection.from === nodeId || connection.to === nodeId) {
                                connectionsToDelete.push(id);
                            }
                        });
                        
                        connectionsToDelete.forEach(id => {
                            this.connections.delete(id);
                        });
                        
                        // 删除节点
                        node.element.remove();
                        this.nodes.delete(nodeId);
                        
                        this.updateConnections();
                        this.updateStats();
                    }
                }
            }

            clearCanvas() {
                if (confirm('确定要清空所有节点吗？')) {
                    this.nodes.clear();
                    this.connections.clear();
                    document.getElementById('canvas').innerHTML = `
                        <div class="empty-state">
                            <i class="fas fa-project-diagram"></i>
                            <h3>开始构建工作流</h3>
                            <p>从左侧面板拖拽节点到此区域开始构建您的工作流</p>
                        </div>
                    `;
                    this.updateStats();
                }
            }
        }

        // 全局函数
        function clearCanvas() {
            orchestrator.clearCanvas();
        }

        function saveWorkflow() {
            orchestrator.saveWorkflow();
        }

        function loadWorkflow() {
            orchestrator.loadDefaultWorkflow();
        }

        function executeWorkflow() {
            orchestrator.executeWorkflow();
        }

        // 初始化
        const orchestrator = new WorkflowOrchestrator();
    </script>
</body>
</html> 