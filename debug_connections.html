<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>调试连接线</title>
    <style>
        body {
            background: #0d1117;
            color: #f0f6fc;
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
        }
        
        .workflow-container {
            position: relative;
            width: 100%;
            height: 800px;
            border: 1px solid #30363d;
            margin: 20px 0;
            overflow: hidden;
        }
        
        .workflow-node {
            position: absolute;
            width: 120px;
            height: 100px;
            background: #21262d;
            border: 2px solid #ff8c00;
            border-radius: 8px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            cursor: pointer;
            z-index: 2;
        }
        
        .workflow-node.completed {
            border-color: #238636;
        }
        
        .node-icon {
            font-size: 24px;
            margin-bottom: 5px;
        }
        
        .node-title {
            font-size: 12px;
            text-align: center;
        }
        
        .node-status {
            font-size: 10px;
            color: #ff8c00;
        }
        
        svg {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1;
            pointer-events: none;
        }
        
        .debug-info {
            background: #21262d;
            border: 1px solid #30363d;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <h1>连接线调试页面</h1>
    <div class="debug-info" id="debugInfo">调试信息将显示在这里...</div>
    
    <div class="workflow-container" id="workflowContainer">
        <svg id="connectionsSvg"></svg>
        
        <!-- 模拟DN工作流节点 -->
        <div class="workflow-node completed" id="node-hold_lot_query" style="left: 250px; top: 50px;">
            <div class="node-icon">HL</div>
            <div class="node-title">Hold Lot List 查询</div>
            <div class="node-status">已完成</div>
        </div>
        
        <div class="workflow-node" id="node-hold_lot_display" style="left: 250px; top: 200px;">
            <div class="node-icon">HD</div>
            <div class="node-title">Hold Lot List 结果展示</div>
            <div class="node-status">待处理</div>
        </div>
        
        <div class="workflow-node" id="node-case_info" style="left: 250px; top: 350px;">
            <div class="node-icon">CI</div>
            <div class="node-title">Case Info</div>
            <div class="node-status">待处理</div>
        </div>
        
        <div class="workflow-node" id="node-scan_info_query" style="left: 50px; top: 500px;">
            <div class="node-icon">SI</div>
            <div class="node-title">Scan Info 查询</div>
            <div class="node-status">待处理</div>
        </div>
        
        <div class="workflow-node" id="node-lot_history_query" style="left: 180px; top: 500px;">
            <div class="node-icon">LH</div>
            <div class="node-title">Lot History 查询</div>
            <div class="node-status">待处理</div>
        </div>
        
        <div class="workflow-node" id="node-map_gallery_query" style="left: 320px; top: 500px;">
            <div class="node-icon">MG</div>
            <div class="node-title">Map Gallery 查询</div>
            <div class="node-status">待处理</div>
        </div>
        
        <div class="workflow-node" id="node-trend_chart_query" style="left: 450px; top: 500px;">
            <div class="node-icon">TC</div>
            <div class="node-title">Trend Chart 查询</div>
            <div class="node-status">待处理</div>
        </div>
    </div>
    
    <button onclick="drawTestConnections()">绘制连接线</button>
    <button onclick="clearConnections()">清除连接线</button>
    <button onclick="testNodePositions()">测试节点位置</button>
    
    <script>
        function log(message) {
            const debugInfo = document.getElementById('debugInfo');
            debugInfo.innerHTML += message + '<br>';
            console.log(message);
        }
        
        function drawTestConnections() {
            log('=== 开始绘制测试连接线 ===');
            
            const svg = document.getElementById('connectionsSvg');
            const container = document.getElementById('workflowContainer');
            
            // 清除现有连接线
            svg.innerHTML = '';
            
            // 创建箭头标记
            const defs = document.createElementNS('http://www.w3.org/2000/svg', 'defs');
            const marker = document.createElementNS('http://www.w3.org/2000/svg', 'marker');
            marker.setAttribute('id', 'arrowhead');
            marker.setAttribute('viewBox', '0 0 10 10');
            marker.setAttribute('refX', '9');
            marker.setAttribute('refY', '3');
            marker.setAttribute('markerWidth', '6');
            marker.setAttribute('markerHeight', '6');
            marker.setAttribute('orient', 'auto');
            
            const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');
            path.setAttribute('d', 'M0,0 L0,6 L9,3 z');
            path.setAttribute('fill', '#1f6feb');
            
            marker.appendChild(path);
            defs.appendChild(marker);
            svg.appendChild(defs);
            
            // 定义连接关系
            const connections = [
                {from: 'node-hold_lot_query', to: 'node-hold_lot_display'},
                {from: 'node-hold_lot_display', to: 'node-case_info'},
                {from: 'node-case_info', to: 'node-scan_info_query'},
                {from: 'node-case_info', to: 'node-lot_history_query'},
                {from: 'node-case_info', to: 'node-map_gallery_query'},
                {from: 'node-case_info', to: 'node-trend_chart_query'}
            ];
            
            // 绘制连接线
            connections.forEach((conn, index) => {
                const fromElement = document.getElementById(conn.from);
                const toElement = document.getElementById(conn.to);
                
                if (fromElement && toElement) {
                    const fromRect = fromElement.getBoundingClientRect();
                    const toRect = toElement.getBoundingClientRect();
                    const containerRect = container.getBoundingClientRect();
                    
                    const fromX = fromRect.left - containerRect.left + fromRect.width / 2;
                    const fromY = fromRect.top - containerRect.top + fromRect.height / 2;
                    const toX = toRect.left - containerRect.left + toRect.width / 2;
                    const toY = toRect.top - containerRect.top + toRect.height / 2;
                    
                    const line = document.createElementNS('http://www.w3.org/2000/svg', 'line');
                    line.setAttribute('x1', fromX);
                    line.setAttribute('y1', fromY);
                    line.setAttribute('x2', toX);
                    line.setAttribute('y2', toY);
                    line.setAttribute('stroke', '#1f6feb');
                    line.setAttribute('stroke-width', '3');
                    line.setAttribute('marker-end', 'url(#arrowhead)');
                    line.setAttribute('opacity', '0.8');
                    
                    svg.appendChild(line);
                    
                    log(`连接线 ${index + 1}: ${conn.from} -> ${conn.to} (${fromX.toFixed(1)},${fromY.toFixed(1)}) -> (${toX.toFixed(1)},${toY.toFixed(1)})`);
                } else {
                    log(`错误: 找不到节点 ${conn.from} 或 ${conn.to}`);
                }
            });
            
            log(`=== 连接线绘制完成，共 ${connections.length} 条 ===`);
        }
        
        function clearConnections() {
            const svg = document.getElementById('connectionsSvg');
            svg.innerHTML = '';
            log('连接线已清除');
        }
        
        function testNodePositions() {
            log('=== 测试节点位置 ===');
            const nodes = document.querySelectorAll('.workflow-node');
            nodes.forEach(node => {
                const rect = node.getBoundingClientRect();
                log(`节点 ${node.id}: 位置 (${rect.left}, ${rect.top}), 大小 ${rect.width}x${rect.height}`);
            });
        }
        
        // 页面加载完成后自动测试
        window.addEventListener('load', () => {
            log('页面加载完成，开始测试...');
            setTimeout(() => {
                testNodePositions();
                drawTestConnections();
            }, 500);
        });
    </script>
</body>
</html>
